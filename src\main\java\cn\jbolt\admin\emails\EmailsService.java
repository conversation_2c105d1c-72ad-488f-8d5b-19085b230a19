package cn.jbolt.admin.emails;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.jbolt.admin.emailaccount.EmailAccountService;
import cn.jbolt.admin.emailtracking.EmailTrackingService;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.common.model.EmailTrackingRecord;
import cn.jbolt.common.model.SysNotice;
import cn.jbolt.common.util.Mimetypes;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.JboltFile;
import cn.jbolt.core.service.JBoltFileService;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import com.jfinal.aop.Inject;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;
import jakarta.mail.Folder;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.Store;
import net.dreamlu.event.EventKit;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.simplejavamail.api.email.Email;
import org.simplejavamail.api.email.EmailPopulatingBuilder;
import org.simplejavamail.api.mailer.Mailer;
import org.simplejavamail.api.mailer.config.TransportStrategy;
import org.simplejavamail.email.EmailBuilder;
import org.simplejavamail.mailer.MailerBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;

import static cn.jbolt.common.util.FileKit.listEmailAttachments;
import static cn.jbolt.common.util.StringKit.noNull;
import static cn.jbolt.mail.gpt.fetch.EmailToDbTool.saveDraftToEmailsEmail;
import static cn.jbolt.mail.gpt.parser.EmailParsingService.createAttachmentPath;

/**
 * 邮件信息管理
 *
 * @ClassName: EmailsService
 * @author: 总管理
 * @date: 2024-05-15 13:05
 */
public class EmailsService extends JBoltBaseService<EmailMessages> {
    private static final Logger log = LoggerFactory.getLogger(EmailsService.class);
    private static final Tika tika = new Tika();
    private final EmailMessages dao = new EmailMessages().dao();

    // 邮件发送线程池配置
    private static final int CORE_POOL_SIZE = 5; // 核心线程数
    private static final int MAX_POOL_SIZE = 10; // 最大线程数
    private static final long KEEP_ALIVE_TIME = 60L; // 空闲线程保持时间（秒）
    private static final int QUEUE_CAPACITY = 100; // 任务队列容量

    // 创建一个线程池用于异步发送邮件
    private static final ExecutorService emailExecutor = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r);
                    t.setName("email-sender-" + t.getId());
                    t.setDaemon(true); // 设置为守护线程，不阻止JVM退出
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 队列满时的拒绝策略：使用调用者所在线程执行任务
    );

    // 邮件发送结果监听器接口
    public interface EmailSendListener {
        void onSuccess(String messageId, String toEmail, String subject);

        void onFailure(String messageId, String toEmail, String subject, Exception e);
    }

    // 已注册的邮件发送结果监听器
    private static final List<EmailSendListener> emailSendListeners = new ArrayList<>();

    /**
     * 注册邮件发送结果监听器
     *
     * @param listener 监听器实例
     */
    public static void addEmailSendListener(EmailSendListener listener) {
        synchronized (emailSendListeners) {
            emailSendListeners.add(listener);
        }
    }

    /**
     * 移除邮件发送结果监听器
     *
     * @param listener 监听器实例
     */
    public static void removeEmailSendListener(EmailSendListener listener) {
        synchronized (emailSendListeners) {
            emailSendListeners.remove(listener);
        }
    }

    /**
     * 通知所有监听器邮件发送成功
     */
    private static void notifyEmailSendSuccess(String messageId, String toEmail, String subject) {
        if (StringUtils.isBlank(messageId) || StringUtils.isBlank(toEmail) || StringUtils.isBlank(subject)) {
            return;
        }
        // 暂时关闭邮件发送成功通知，只有邮件发送失败通知
        // SysNotice sysNotice = new SysNotice();
        // sysNotice.setTitle("邮件发送成功:" + subject);
        // sysNotice.setContent("邮件发送成功:" + subject);
        // sysNotice.setType(6);
        // sysNotice.setReceiverType(5);
        // sysNotice.setPriorityLevel(1);
        // sysNotice.setReceiverValue(String.valueOf(JBoltUserKit.getUserId()));
        // sysNotice.setCreateUserId(JBoltUserKit.getUserId());
        // sysNotice.setCreateTime(new Date());
        // sysNotice.setDelFlag(false);
        // if (sysNotice.save()) {
        //     EventKit.post(sysNotice);
        // }
        synchronized (emailSendListeners) {
            for (EmailSendListener listener : emailSendListeners) {
                try {
                    listener.onSuccess(messageId, toEmail, subject);
                } catch (Exception e) {
                    log.error("邮件发送成功通知监听器异常", e);
                }
            }
        }
    }

    /**
     * 通知所有监听器邮件发送失败
     */
    private static void notifyEmailSendFailure(String messageId, String toEmail, String subject, Exception e) {
        // 构建通知内容，包含错误信息和重新编辑链接
        String errorMsg = e != null ? e.getMessage() : "未知错误";
        StringBuilder noticeContent = new StringBuilder();
        noticeContent.append("邮件发送失败！\n");
        noticeContent.append("收件人：").append(toEmail).append("\n");
        noticeContent.append("主题：").append(subject).append("\n");
        noticeContent.append("错误信息：").append(errorMsg).append("\n\n");

        // 尝试从发送上下文中获取完整的邮件信息并保存草稿
        String draftId = null;
        try {
            Map<String, Object> context = sendingEmailContext.get();
            if (context != null) {
                String fromEmail = (String) context.get("fromEmail");
                String ccEmail = (String) context.get("ccEmail");
                String content = (String) context.get("content");

                // 使用完整信息保存草稿
                draftId = saveFailedEmailAsDraft(messageId, fromEmail, toEmail, ccEmail, subject, content, e);

                if (draftId != null) {
                    noticeContent.append("已自动保存草稿，点击链接重新编辑：\n");
                    noticeContent.append("<a href=\"/admin/emailMessages/composeEmail?draftId=")
                            .append(draftId)
                            .append("\" target=\"_blank\" style=\"color: #007bff; text-decoration: underline;\">")
                            .append("重新编辑发送</a>");
                } else {
                    noticeContent.append("草稿保存失败，请手动重新编辑邮件。");
                }
            } else {
                // 如果没有上下文信息，使用基本信息创建草稿
                draftId = saveFailedEmailAsDraft(messageId, null, toEmail, null, subject, null, e);
                if (draftId != null) {
                    noticeContent.append("已保存基本信息草稿，点击链接重新编辑：\n");
                    noticeContent.append("<a href=\"/admin/emailMessages/composeEmail?draftId=")
                            .append(draftId)
                            .append("\" target=\"_blank\" style=\"color: #007bff; text-decoration: underline;\">")
                            .append("重新编辑发送</a>");
                } else {
                    noticeContent.append("草稿保存失败，请手动重新编辑邮件。");
                }
            }
        } catch (Exception ex) {
            LogKit.error("保存邮件发送失败草稿时出错", ex);
            noticeContent.append("草稿保存失败，请手动重新编辑邮件。");
        } finally {
            // 清理线程本地变量
            sendingEmailContext.remove();
        }

        SysNotice sysNotice = new SysNotice();
        sysNotice.setTitle("邮件发送失败：" + subject);
        sysNotice.setContent(noticeContent.toString());
        sysNotice.setType(6);
        sysNotice.setReceiverType(5);
        sysNotice.setPriorityLevel(1);
        sysNotice.setReceiverValue(String.valueOf(JBoltUserKit.getUserId()));
        sysNotice.setCreateUserId(JBoltUserKit.getUserId());
        sysNotice.setCreateTime(new Date());
        sysNotice.setDelFlag(false);

        // 如果保存了草稿，将草稿ID添加到通知的扩展信息中
        if (draftId != null) {
            sysNotice.put("draftId", draftId);
            sysNotice.put("actionUrl", "/admin/emailMessages/composeEmail?draftId=" + draftId);
            sysNotice.put("actionText", "重新编辑发送");
        }

        if (sysNotice.save()) {
            EventKit.post(sysNotice);
        }

        synchronized (emailSendListeners) {
            for (EmailSendListener listener : emailSendListeners) {
                try {
                    listener.onFailure(messageId, toEmail, subject, e);
                } catch (Exception ex) {
                    log.error("邮件发送失败通知监听器异常", ex);
                }
            }
        }
    }

    @Inject
    private EmailNameCache emailNameCache;

    @Inject
    private EmailAccountService emailAccountService;

    @Inject
    private EmailTrackingService emailTrackingService;

    private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";

    // 邮件跟踪配置
    private static final String TRACKING_BASE_URL = "/track"; // 可以从配置文件读取
    private static boolean trackingEnabled = true; // 可以从配置文件读取

    /**
     * 初始化邮件跟踪服务
     */
    public void initEmailTracking() {
        if (emailTrackingService != null && trackingEnabled) {
            // 注册跟踪服务为邮件发送监听器
            addEmailSendListener(emailTrackingService);
            LogKit.info("邮件跟踪服务已初始化并注册为监听器");
        }
    }

    /**
     * 系统启动时初始化
     */
    public void onApplicationStart() {
        try {
            // 初始化邮件跟踪功能
            initEmailTracking();
            LogKit.info("邮件服务初始化完成");
        } catch (Exception e) {
            LogKit.error("邮件服务初始化失败", e);
        }
    }

    /**
     * 获取跟踪基础URL
     */
    private String getTrackingBaseUrl() {
        // 这里可以从配置文件或系统配置中读取
        // 暂时使用硬编码，实际应该从配置中获取
        return Db.queryStr("SELECT config_value FROM email_tracking_config WHERE config_key = 'tracking.base_url'");
        //return "http://enter.theolympiastone.com:8001" + TRACKING_BASE_URL;
    }

    /**
     * 将跟踪像素注入到HTML内容中
     */
    private String injectTrackingPixel(String htmlContent, String trackingPixelHtml) {
        if (StrKit.isBlank(htmlContent) || StrKit.isBlank(trackingPixelHtml)) {
            return htmlContent;
        }

        try {
            // 查找body结束标签
            int bodyEndIndex = htmlContent.toLowerCase().lastIndexOf("</body>");
            if (bodyEndIndex != -1) {
                // 在body结束标签前插入跟踪像素
                return htmlContent.substring(0, bodyEndIndex) +
                       trackingPixelHtml +
                       htmlContent.substring(bodyEndIndex);
            } else {
                // 如果没有body标签，直接在HTML末尾添加
                return htmlContent + trackingPixelHtml;
            }
        } catch (Exception e) {
            LogKit.error("注入跟踪像素到HTML失败", e);
            return htmlContent;
        }
    }

    @Override
    protected EmailMessages dao() {
        return dao;
    }

    /**
     * 保存后的回调处理
     */
    protected String afterSave(EmailMessages emailMessages) {
        return "";
    }

    @Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }

    public Page<EmailMessages> getAdminDatas(int pageNumber, int pageSize, String q, Set<String> emailSet) {
        if (emailSet.isEmpty()) {
            return null;
        }
        Sql sql = selectSql().page(pageNumber, pageSize);
        sql.likeMulti(q, "from_address", "subject", "to_address", "cc_address", "content_html");
        sql.orderBy("sent_date", true);

        Sql clientIdInSql = Sql.me(this.dbType).from("emails_email").select("emails_id").in("email",
                emailSet.toArray());
        sql.inSql("id", clientIdInSql);

        Page<EmailMessages> page = paginate(sql);

        // 处理显示名称
        if (page != null && page.getList() != null) {
            for (EmailMessages message : page.getList()) {
                // 处理发件人显示名称
                String fromAddress = message.getFromAddress();
                if (fromAddress != null) {
                    String fromName = emailNameCache.getDisplayName(fromAddress);
                    message.put("from_name", fromName);
                }

                // 处理收件人显示名称
                String toAddress = message.getToAddress();
                if (toAddress != null) {
                    String toName = emailNameCache.getDisplayName(toAddress);
                    message.put("to_name", toName);
                }
            }
        }

        return page;
    }

    public EmailMessages findByMd5(String username, String md5) {
        return dao.findFirst("select * from emails where email=? and md5 = ?", username, md5);
    }

    /**
     * 切换本地邮件已读状态（不影响服务器）
     */
    public Ret toggleReadStatus(String id) {
        EmailMessages email = findById(id);
        if (email == null) {
            return fail("邮件不存在");
        }

        // 只修改本地数据库中的状态
        email.setIsRead(!email.getIsRead());
        boolean success = email.update();
        return success ? SUCCESS : fail("操作失败");
    }

    /**
     * 发送邮件 - 使用回调函数获取发送结果
     *
     * @param fromEmail       发件人
     * @param toEmail         收件人
     * @param ccEmail         抄送
     * @param subject         主题
     * @param content         内容
     * @param attachments     附件
     * @param successCallback 发送成功回调
     * @param failureCallback 发送失败回调
     * @return 提交结果
     */
    public Ret sendEmail(String fromEmail, String toEmail, String ccEmail, String subject, String content,
                         List<UploadFile> attachments, Consumer<String> successCallback, Consumer<Exception> failureCallback) {
        Ret ret = sendEmailAsync(fromEmail, toEmail, ccEmail, subject, content, attachments);

        // 通过返回的messageId，注册一个一次性的监听器
        if (ret.isOk() && successCallback != null && failureCallback != null) {
            final String messageId = ret.getStr("messageId");

            addEmailSendListener(new EmailSendListener() {
                @Override
                public void onSuccess(String msgId, String email, String subj) {
                    if (messageId.equals(msgId)) {
                        successCallback.accept(messageId);
                        removeEmailSendListener(this);
                    }
                }

                @Override
                public void onFailure(String msgId, String email, String subj, Exception e) {
                    if (messageId.equals(msgId)) {
                        failureCallback.accept(e);
                        removeEmailSendListener(this);
                    }
                }
            });
        }

        return ret;
    }

    /**
     * 发送邮件 - 使用CompletableFuture返回异步结果
     *
     * @param fromEmail   发件人
     * @param toEmail     收件人
     * @param ccEmail     抄送
     * @param subject     主题
     * @param content     内容
     * @param attachments 附件
     * @return CompletableFuture<Boolean> 发送结果的Future
     */
    public CompletableFuture<Boolean> sendEmailWithFuture(String fromEmail, String toEmail, String ccEmail,
                                                          String subject, String content, List<UploadFile> attachments) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        sendEmail(fromEmail, toEmail, ccEmail, subject, content, attachments,
                messageId -> future.complete(true),
                future::completeExceptionally);

        return future;
    }

    /**
     * 发送邮件 - 异步执行
     */
    public Ret sendEmailAsync(String fromEmail, String toEmail, String ccEmail, String subject, String content,
                              List<UploadFile> attachments) {
        try {
            // 邮箱验证和参数准备
            EmailAccount emailAccount;
            if (fromEmail.matches(EMAIL_REGEX)) {
                // 如果是邮箱格式，使用 username 查找
                emailAccount = emailAccountService.findByEmail(fromEmail);
            } else {
                // 如果不是邮箱格式，按 ID 查找
                emailAccount = emailAccountService.findById(fromEmail);
            }
            if (emailAccount == null) {
                return Ret.fail("发件人邮箱账号不存在");
            }

            // 检查邮箱配置是否有效
            if (!emailAccount.getValid()) {
                return Ret.fail("发件人邮箱配置无效，请先检查配置");
            }

            // 生成唯一消息ID，用于跟踪发送结果
            String messageId = UUID.randomUUID().toString();

            // 保存邮件发送上下文，用于发送失败时保存草稿
            Map<String, Object> context = new HashMap<>();
            context.put("messageId", messageId);
            context.put("fromEmail", emailAccount.getUsername()); // 使用实际的邮箱地址
            context.put("toEmail", toEmail);
            context.put("ccEmail", ccEmail);
            context.put("subject", subject);
            context.put("content", content);
            context.put("attachments", attachments);
            sendingEmailContext.set(context);

            // 创建HTML内容解析器，处理内联图片
            Document doc = Jsoup.parse(content);
            Elements imgElements = doc.select("img[src]");

            // 处理HTML内容中的图片引用

            // 第一遍扫描：预处理所有图片，但不修改src属性
            for (org.jsoup.nodes.Element img : imgElements) {
                String src = img.attr("src");

                // 跳过外部URL和base64图片
                if (src.startsWith("data:") || src.startsWith("http://") || src.startsWith("https://")) {
                    continue;
                }

                // 保存原始路径，以便后续处理时使用
                img.attr("data-original-src", src);
            }

            // 保存处理后的HTML内容
            final String processedHtml = doc.html();

            // 复制附件列表，防止异步处理时发生并发问题
            final List<UploadFile> attachmentsCopy = new ArrayList<>(attachments);

            // 将邮件发送操作放入线程池异步执行
            CompletableFuture.runAsync(() -> {
                try (Mailer mailer = MailerBuilder
                        .withSMTPServer(emailAccount.getSmtpHost(), Integer.parseInt(emailAccount.getSmtpPort()),
                                emailAccount.getUsername(), emailAccount.getPassword())
                        .withTransportStrategy(TransportStrategy.SMTPS)
                        .buildMailer()) {

                    // 重新设置发送上下文到当前线程
                    sendingEmailContext.set(context);

                    // 创建邮件构建器
                    EmailPopulatingBuilder emailPopulatingBuilder = EmailBuilder.startingBlank()
                            .from(emailAccount.getUsername()) // 使用邮箱账户的用户名作为发件人
                            .to(toEmail)
                            .withSubject(subject);

                    // 处理抄送
                    if (!StringUtils.isEmpty(ccEmail)) {
                        emailPopulatingBuilder.cc(ccEmail);
                    }

                    // 先创建一个临时HTML文档，稍后会更新
                    Document tempDoc = Jsoup.parse(processedHtml);
                    Elements tempImgElements = tempDoc.select("img[data-original-src]");

                    // 处理图片引用
                    for (org.jsoup.nodes.Element img : tempImgElements) {
                        String originalSrc = img.attr("data-original-src");

                        try {
                            // 获取图片的绝对路径
                            String imagePath;
                            if(originalSrc.contains("common/file?filePath=")){
                                imagePath = originalSrc.split("common/file?filePath=")[1];
                            }else{
                                if (originalSrc.startsWith("/")) {
                                    // 以 / 开头的路径，直接拼接到WebRoot
                                    imagePath = com.jfinal.kit.PathKit.getWebRootPath() + originalSrc;
                                } else {
                                    // 相对路径，需要拼接到WebRoot
                                    imagePath = com.jfinal.kit.PathKit.getWebRootPath() + "/" + originalSrc;
                                }
                            }

                            File imageFile = new File(imagePath);
                            if (imageFile.exists() && imageFile.isFile()) {
                                // 获取文件名和生成唯一的CID
                                String fileName = imageFile.getName();
                                String cid;

                                // 如果原始路径以 /upload/ 开头，使用路径作为CID（去掉/upload/前缀，替换/为_）
                                if (originalSrc.startsWith("/upload/")) {
                                    cid = originalSrc.substring("/upload/".length()).replace("/", "_");
                                } else {
                                    // 其他情况使用文件名作为CID
                                    cid = fileName;
                                }

                                // 将CID引用设置到img标签
                                img.attr("src", "cid:" + cid);

                                // 确定MIME类型
                                int dotIndex = fileName.lastIndexOf(".");
                                String suffix = dotIndex > -1 ? fileName.substring(dotIndex + 1) : "jpg";
                                String mimeType = Mimetypes.map.getOrDefault(suffix, "image/jpeg");

                                // 添加内嵌图片
                                emailPopulatingBuilder.withEmbeddedImage(
                                        cid,
                                        FileUtils.readFileToByteArray(imageFile),
                                        mimeType
                                );

                                log.info("添加内嵌图片，fileName: {}, contentId: {}, mimeType: {}", fileName, cid, mimeType);
                            } else {
                                log.warn("图片文件不存在: {}", imagePath);
                            }
                        } catch (Exception e) {
                            log.error("处理内嵌图片失败: {}", originalSrc, e);
                        }

                        // 移除临时属性
                        img.removeAttr("data-original-src");
                    }

                    // 使用处理后的HTML内容
                    String finalHtml = tempDoc.html();

                    // 注入邮件跟踪像素（如果启用）
                    if (trackingEnabled && emailTrackingService != null) {
                        try {
                            // 创建跟踪记录
                            EmailTrackingRecord trackingRecord = emailTrackingService.createTrackingRecord(
                                messageId,
                                emailAccount.getUsername(),
                                toEmail,
                                ccEmail,
                                subject,
                                null // emailMessageId 暂时为null，发送成功后可以关联
                            );

                            if (trackingRecord != null) {
                                // 生成跟踪像素HTML
                                String baseUrl = getTrackingBaseUrl(); // 需要实现这个方法
                                String trackingPixelHtml = emailTrackingService.generateTrackingPixelHtml(
                                    trackingRecord.getTrackingId(), baseUrl
                                );

                                if (StrKit.notBlank(trackingPixelHtml)) {
                                    // 将跟踪像素注入到HTML的body结束标签前
                                    finalHtml = injectTrackingPixel(finalHtml, trackingPixelHtml);
                                    LogKit.info("已注入邮件跟踪像素: trackingId={}", trackingRecord.getTrackingId());
                                }
                            }
                        } catch (Exception e) {
                            LogKit.error("注入邮件跟踪像素失败: messageId={}", messageId, e);
                            // 跟踪失败不影响邮件发送，继续处理
                        }
                    }

                    emailPopulatingBuilder.withHTMLText(finalHtml);

                    // 处理普通附件
                    for (UploadFile attachment : attachmentsCopy) {
                        File file = attachment.getFile();
                        String fileName = file.getName();
                        int dotIndex = fileName.lastIndexOf(".");
                        if (dotIndex > -1 && file.exists() && file.length() > 0) {
                            String suffix = fileName.substring(dotIndex + 1);
                            emailPopulatingBuilder.withAttachment(fileName, FileUtils.readFileToByteArray(file),
                                    Mimetypes.map.getOrDefault(suffix, "application/octet-stream"));
                        }
                    }

                    Email email = emailPopulatingBuilder.buildEmail();

                    // 发送邮件
                    log.info("开始发送邮件，messageId: {}, 收件人: {}, 主题: {}", messageId, toEmail, subject);
                    mailer.sendMail(email);
                    log.info("邮件发送成功，messageId: {}, 收件人: {}, 主题: {}", messageId, toEmail, subject);

                    // 通知发送成功，并清理上下文
                    sendingEmailContext.remove();
                    notifyEmailSendSuccess(messageId, toEmail, subject);

                    // 关闭Mailer
                    mailer.shutdownConnectionPool();
                } catch (Exception e) {
                    log.error("异步发送邮件失败，messageId: {}, 收件人: {}, 主题: {}, 错误: {}",
                            messageId, toEmail, subject, e.getMessage());

                    // 通知发送失败
                    notifyEmailSendFailure(messageId, toEmail, subject, e);
                }
            }, emailExecutor);

            return Ret.ok("邮件发送请求已提交，将在后台处理").set("messageId", messageId);
        } catch (Exception e) {
            // 记录详细日志
            log.error("邮件发送请求处理异常", e);
            return Ret.fail("邮件发送异常: " + e.getMessage());
        }
    }

    /**
     * 兼容旧API的发送邮件方法
     */
    public Ret sendEmail(String fromEmail, String toEmail, String ccEmail, String subject, String content,
                         List<UploadFile> attachments) {
        return sendEmailAsync(fromEmail, toEmail, ccEmail, subject, content, attachments);
    }

    /**
     * 发送邮件，支持已上传的附件ID
     *
     * @param fromEmail           发件人
     * @param toEmail             收件人
     * @param ccEmail             抄送
     * @param subject             主题
     * @param content             内容
     * @param attachments         新上传的附件
     * @param forwardFiles        转发的原始附件
     * @param uploadedAttachmentIds 已上传的附件ID列表
     * @return 发送结果
     */
    public Ret sendEmail(String fromEmail, String toEmail, String ccEmail, String subject, String content,
                         List<UploadFile> attachments, List<File> forwardFiles, String[] uploadedAttachmentIds) {
        try {
            // 获取发件人账户信息
            EmailAccount account = new EmailAccount().dao().findFirst("select * from email_account where username = ?", fromEmail);
            if (account == null) {
                return Ret.fail("发件人账户不存在");
            }

            // 创建邮件构建器
            EmailPopulatingBuilder emailBuilder = EmailBuilder.startingBlank()
                    .from(account.getNickname(), fromEmail)
                    .to(toEmail)
                    .withSubject(subject);

            // 处理抄送
            if (StrKit.notBlank(ccEmail)) {
                emailBuilder.cc(ccEmail);
            }

            // 处理HTML内容中的内联图片
            Document doc = Jsoup.parse(content);
            Elements imgElements = doc.select("img[src]");
            for (Element img : imgElements) {
                String src = img.attr("src");
                if (src.startsWith("data:image/")) {
                    // 处理base64图片
                    String[] parts = src.split(",");
                    if (parts.length == 2) {
                        String mimeType = parts[0].substring(5, parts[0].indexOf(";"));
                        byte[] imageBytes = Base64.getDecoder().decode(parts[1]);
                        // 生成正确的CID，避免使用Math.random()产生小数点
                        String cid = "image_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "");
                        emailBuilder.withEmbeddedImage(cid, imageBytes, mimeType);
                        img.attr("src", "cid:" + cid);
                        log.info("添加base64内嵌图片，contentId: {}, mimeType: {}", cid, mimeType);
                    }
                } else if (src.startsWith("/upload/")) {
                    // 处理上传的图片文件，转换为内联图片
                    try {
                        String imagePath = com.jfinal.kit.PathKit.getWebRootPath() + src;
                        File imageFile = new File(imagePath);
                        if (imageFile.exists() && imageFile.isFile()) {
                            // 使用文件名作为CID
                            String fileName = imageFile.getName();
                            String cid = src.substring("/upload/".length()).replace("/", "_");

                            // 确定MIME类型
                            String mimeType = "image/jpeg"; // 默认类型
                            String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                            switch (extension) {
                                case "png":
                                    mimeType = "image/png";
                                    break;
                                case "gif":
                                    mimeType = "image/gif";
                                    break;
                                case "jpg":
                                case "jpeg":
                                    mimeType = "image/jpeg";
                                    break;
                            }

                            emailBuilder.withEmbeddedImage(cid, FileUtils.readFileToByteArray(imageFile), mimeType);
                            img.attr("src", "cid:" + cid);
                            log.info("添加内嵌图片，fileName: {}, contentId: {}, mimeType: {}", fileName, cid, mimeType);
                        } else {
                            log.warn("图片文件不存在: {}", imagePath);
                        }
                    } catch (Exception e) {
                        log.error("处理内嵌图片失败: {}", src, e);
                    }
                }
            }

            content = doc.html();

            // 生成唯一的消息ID（提前生成用于跟踪）
            String messageId = UUID.randomUUID().toString();

            // 注入邮件跟踪像素（如果启用）
            if (trackingEnabled && emailTrackingService != null) {
                try {
                    // 创建跟踪记录
                    EmailTrackingRecord trackingRecord = emailTrackingService.createTrackingRecord(
                        messageId,
                        fromEmail,
                        toEmail,
                        ccEmail,
                        subject,
                        null // emailMessageId 暂时为null
                    );

                    if (trackingRecord != null) {
                        // 生成跟踪像素HTML
                        String baseUrl = getTrackingBaseUrl();
                        String trackingPixelHtml = emailTrackingService.generateTrackingPixelHtml(
                            trackingRecord.getTrackingId(), baseUrl
                        );

                        if (StrKit.notBlank(trackingPixelHtml)) {
                            // 将跟踪像素注入到HTML中
                            content = injectTrackingPixel(content, trackingPixelHtml);
                            LogKit.info("已注入邮件跟踪像素: trackingId={}", trackingRecord.getTrackingId());
                        }
                    }
                } catch (Exception e) {
                    LogKit.error("注入邮件跟踪像素失败: messageId={}", messageId, e);
                    // 跟踪失败不影响邮件发送，继续处理
                }
            }

            emailBuilder.withHTMLText(content);

            // 添加新上传的附件
            if (attachments != null) {
                for (UploadFile uploadFile : attachments) {
                    File file = uploadFile.getFile();
                    if (file.exists() && file.isFile()) {
                        String mimeType = tika.detect(file);
                        emailBuilder.withAttachment(uploadFile.getOriginalFileName(), FileUtils.readFileToByteArray(file), mimeType);
                    }
                }
            }

            // 添加转发的原始附件
            if (forwardFiles != null) {
                for (File file : forwardFiles) {
                    if (file.exists() && file.isFile()) {
                        String mimeType = tika.detect(file);
                        emailBuilder.withAttachment(file.getName(), FileUtils.readFileToByteArray(file), mimeType);
                    }
                }
            }

            // 添加已上传的附件
            if (uploadedAttachmentIds != null && uploadedAttachmentIds.length > 0) {
                JBoltFileService fileService = new JBoltFileService();
                Set<String> processedIds = new HashSet<>(); // 用于防止重复处理

                for (String attachmentIdParam : uploadedAttachmentIds) {
                    if (StrKit.notBlank(attachmentIdParam)) {
                        // 处理可能的逗号分隔的ID字符串
                        String[] attachmentIdSplit = attachmentIdParam.split(",");
                        for (String singleId : attachmentIdSplit) {
                            String trimmedId = singleId.trim();
                            if (StrKit.notBlank(trimmedId) && !processedIds.contains(trimmedId)) {
                                processedIds.add(trimmedId);
                                JboltFile jboltFile = fileService.findById(trimmedId);
                                if (jboltFile != null) {
                                    File file = new File(jboltFile.getLocalPath());
                                    if (file.exists() && file.isFile()) {
                                        String mimeType = tika.detect(file);
                                        emailBuilder.withAttachment(jboltFile.getFileName(), FileUtils.readFileToByteArray(file), mimeType);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 构建邮件
            Email email = emailBuilder.buildEmail();

            // 异步发送邮件
            emailExecutor.submit(() -> {
                try (Mailer mailer = MailerBuilder
                        .withSMTPServer(account.getSmtpHost(), Integer.parseInt(account.getSmtpPort()), account.getUsername(), account.getPassword())
                        .withTransportStrategy(TransportStrategy.SMTPS)
                        .withSessionTimeout(10 * 60 * 1000) // 10分钟超时
                        .withDebugLogging(false)
                        .buildMailer()) {
                    // 发送邮件
                    mailer.sendMail(email);

                    // 通知成功
                    notifyEmailSendSuccess(messageId, toEmail, subject);
                    log.info("邮件发送成功: {}", messageId);
                } catch (Exception e) {
                    // 通知失败
                    notifyEmailSendFailure(messageId, toEmail, subject, e);
                    log.error("邮件发送失败: {}", messageId, e);
                }
            });

            return Ret.ok("邮件发送请求已提交，将在后台处理").set("messageId", messageId);
        } catch (Exception e) {
            // 记录详细日志
            log.error("邮件发送请求处理异常", e);
            return Ret.fail("邮件发送异常: " + e.getMessage());
        }
    }

    /**
     * 发送邮件，支持转发附件
     *
     * @param fromEmail    发件人
     * @param toEmail      收件人
     * @param ccEmail      抄送
     * @param subject      主题
     * @param content      内容
     * @param attachments  新上传的附件
     * @param forwardFiles 转发的原始附件
     * @return 发送结果
     */
    public Ret sendEmail(String fromEmail, String toEmail, String ccEmail, String subject, String content,
                         List<UploadFile> attachments, List<File> forwardFiles) {
        try {
            // 获取发件人账户信息
            EmailAccount account = new EmailAccount().dao().findFirst("select * from email_account where username = ?", fromEmail);
            if (account == null) {
                return Ret.fail("发件人账户不存在");
            }

            // 创建邮件构建器
            EmailPopulatingBuilder emailBuilder = EmailBuilder.startingBlank()
                    .from(account.getNickname(), fromEmail)
                    .to(toEmail)
                    .withSubject(subject);

            // 添加抄送
            if (StringUtils.isNotBlank(ccEmail)) {
                String[] ccEmails = ccEmail.split("[,;]");
                for (String cc : ccEmails) {
                    cc = cc.trim();
                    if (StringUtils.isNotBlank(cc)) {
                        emailBuilder.cc(cc);
                    }
                }
            }

            // content 正文中的图片，路径是 /upload/ 开头
            // 需要将这些图片转换为内联图片
            Document doc = Jsoup.parse(content);
            Elements imgElements = doc.select("img[src]");
            for (org.jsoup.nodes.Element img : imgElements) {
                String src = img.attr("src");
                if (src.startsWith("/upload/")) {
                    // 内联图片，需要转换为cid
                    String cid = src.substring("/upload/".length());
                    File imageFile = new File(com.jfinal.kit.PathKit.getWebRootPath() + src);

                    // 确定正确的MIME类型
                    String mimeType = "image/jpeg"; // 默认类型
                    String fileName = imageFile.getName();
                    if (fileName.contains(".")) {
                        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                        switch (extension) {
                            case "png":
                                mimeType = "image/png";
                                break;
                            case "gif":
                                mimeType = "image/gif";
                                break;
                            case "jpg":
                            case "jpeg":
                                mimeType = "image/jpeg";
                                break;
                            case "webp":
                                mimeType = "image/webp";
                                break;
                        }
                    }

                    emailBuilder.withEmbeddedImage(cid, FileUtils.readFileToByteArray(imageFile), mimeType);
                    img.attr("src", "cid:" + cid);
                    log.info("添加内嵌图片(回复)，fileName: {}, contentId: {}, mimeType: {}", imageFile.getName(), cid, mimeType);
                }
            }
            content = doc.html();

            // 生成唯一的消息ID（提前生成用于跟踪）
            String messageId = UUID.randomUUID().toString();

            // 注入邮件跟踪像素（如果启用）
            if (trackingEnabled && emailTrackingService != null) {
                try {
                    // 创建跟踪记录
                    EmailTrackingRecord trackingRecord = emailTrackingService.createTrackingRecord(
                        messageId,
                        fromEmail,
                        toEmail,
                        ccEmail,
                        subject,
                        null // emailMessageId 暂时为null
                    );

                    if (trackingRecord != null) {
                        // 生成跟踪像素HTML
                        String baseUrl = getTrackingBaseUrl();
                        String trackingPixelHtml = emailTrackingService.generateTrackingPixelHtml(
                            trackingRecord.getTrackingId(), baseUrl
                        );

                        if (StrKit.notBlank(trackingPixelHtml)) {
                            // 将跟踪像素注入到HTML中
                            content = injectTrackingPixel(content, trackingPixelHtml);
                            LogKit.info("已注入邮件跟踪像素: trackingId={}", trackingRecord.getTrackingId());
                        }
                    }
                } catch (Exception e) {
                    LogKit.error("注入邮件跟踪像素失败: messageId={}", messageId, e);
                    // 跟踪失败不影响邮件发送，继续处理
                }
            }

            emailBuilder.withHTMLText(content);

            // 添加新上传的附件
            if (attachments != null) {
                for (UploadFile uploadFile : attachments) {
                    File file = uploadFile.getFile();
                    if (file.exists() && file.isFile()) {
                        String mimeType = tika.detect(file);
                        emailBuilder.withAttachment(uploadFile.getOriginalFileName(), FileUtils.readFileToByteArray(file), mimeType);
                    }
                }
            }

            // 添加转发的原始附件
            if (forwardFiles != null) {
                for (File file : forwardFiles) {
                    if (file.exists() && file.isFile()) {
                        String mimeType = tika.detect(file);
                        emailBuilder.withAttachment(file.getName(), FileUtils.readFileToByteArray(file), mimeType);
                    }
                }
            }

            // 构建邮件
            Email email = emailBuilder.buildEmail();

            // 异步发送邮件
            emailExecutor.submit(() -> {
                try (Mailer mailer = MailerBuilder
                        .withSMTPServer(account.getSmtpHost(), Integer.parseInt(account.getSmtpPort()), account.getUsername(), account.getPassword())
                        .withTransportStrategy(TransportStrategy.SMTPS)
                        .withSessionTimeout(10 * 60 * 1000) // 10分钟超时
                        .withDebugLogging(false)
                        .buildMailer()) {
                    // 发送邮件
                    mailer.sendMail(email);

                    // 通知成功
                    notifyEmailSendSuccess(messageId, toEmail, subject);
                    log.info("邮件发送成功: {}", messageId);
                } catch (Exception e) {
                    // 通知失败
                    notifyEmailSendFailure(messageId, toEmail, subject, e);
                    log.error("邮件发送失败: {}", messageId, e);
                }
            });

            return Ret.ok("邮件发送请求已提交，将在后台处理").set("messageId", messageId);
        } catch (Exception e) {
            // 记录详细日志
            log.error("邮件发送请求处理异常", e);
            return Ret.fail("邮件发送异常: " + e.getMessage());
        }
    }

    /**
     * 关闭邮件发送线程池（在系统关闭时调用）
     */
    public static void shutdownEmailExecutor() {
        try {
            log.info("开始关闭邮件发送线程池...");
            emailExecutor.shutdown();
            if (!emailExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("邮件发送线程池未能在30秒内完全关闭，执行强制关闭");
                emailExecutor.shutdownNow();
            }
            log.info("邮件发送线程池已关闭");
        } catch (InterruptedException e) {
            log.error("关闭邮件发送线程池时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 保存草稿 - 支持已上传的附件ID
     */
    public Ret saveDraft(String fromEmail, String toEmail, String ccEmail, String subject,
                         String content, List<UploadFile> attachments, String originalEmailId, String draftId, String[] uploadedAttachmentIds) {
        try {
            EmailMessages draft;
            boolean isUpdate = false;

            if (isOk(draftId)) {
                // 如果有draftId，尝试加载现有草稿进行更新
                draft = findById(draftId);
                if (draft != null) {
                    isUpdate = true;
                } else {
                    // 如果找不到现有草稿，创建新的
                    draft = new EmailMessages();
                }
            } else {
                // 没有draftId，创建新草稿
                draft = new EmailMessages();
            }

            // 设置草稿字段
            if (!isUpdate) {
                draft.setMessageId(UUID.randomUUID().toString());
                draft.setCreatedAt(new Date());
                draft.setIsRead(true); // 草稿默认已读
                draft.setFolderName("草稿箱"); // 设置文件夹名称
            }

            draft.setSentDate(new Date());
            draft.setFromAddress(fromEmail);
            draft.setToAddress(toEmail);
            draft.setCcAddress(ccEmail);
            draft.setSubject(subject);
            draft.setContentHtml(content);
            draft.setUpdatedAt(new Date());
            draft.setIsDraft(true);
            draft.setOriginalEmailId(originalEmailId);

            // 处理附件 - 检查是否有附件
            boolean hasAttachments = false;
            if (attachments != null && !attachments.isEmpty()) {
                hasAttachments = true;
            }
            if (uploadedAttachmentIds != null && uploadedAttachmentIds.length > 0) {
                for (String attachmentId : uploadedAttachmentIds) {
                    if (StrKit.notBlank(attachmentId)) {
                        hasAttachments = true;
                        break;
                    }
                }
            }
            draft.setHasAttachments(hasAttachments);

            boolean success;
            if (isUpdate) {
                success = draft.update();
            } else {
                success = draft.save();
            }
            saveDraftToEmailsEmail(draft);
            if (success) {
                return Ret.ok("msg", "保存成功").set("data", draft);
            } else {
                return fail("保存失败");
            }
        } catch (Exception e) {
            LogKit.error("保存草稿失败", e);
            return fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 将所有邮件标记为已读
     */
    public Ret markAllAsRead(String companyId) {
        try {
            // 只更新本地数据库中的状态
            int updatedCount = Db.update("update email_messages set is_read = true where is_read = false and id in " +
                    "(select emails_id from emails_email where email in " +
                    "(select email from client where id in " +
                    "(select client_id from company_client where company_id=?)))", companyId);
            return Ret.ok("msg", "成功标记 " + updatedCount + " 封邮件为已读");
        } catch (Exception e) {
            LogKit.error("标记所有邮件为已读失败", e);
            return fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取邮箱文件夹列表
     */
    public Ret getFolderList(Long accountId) {
        try {
            EmailAccount account = emailAccountService.findById(accountId);
            if (account == null) {
                return fail("邮箱账户不存在");
            }

            Properties props = new Properties();
            props.put("mail.imap.host", account.getImapHost());
            props.put("mail.imap.port", account.getImapPort());
            props.put("mail.imap.ssl.enable", "true");

            Session session = Session.getInstance(props);
            Store store = session.getStore("imap");
            store.connect(account.getImapHost(), account.getUsername(), account.getPassword());

            // 获取根文件夹
            Folder defaultFolder = store.getDefaultFolder();
            if (defaultFolder == null) {
                return fail("无法获取邮箱文件夹");
            }

            // 获取所有文件夹
            List<Map<String, String>> folderList = new ArrayList<>();
            getAllFoldersRecursive(defaultFolder, folderList);

            store.close();
            return Ret.ok("data", folderList);
        } catch (Exception e) {
            LogKit.error("获取文件夹列表失败", e);
            return fail("获取文件夹列表失败：" + e.getMessage());
        }
    }

    private void getAllFoldersRecursive(Folder folder, List<Map<String, String>> folderList) throws MessagingException {
        if ((folder.getType() & Folder.HOLDS_MESSAGES) != 0) {
            Map<String, String> folderInfo = new HashMap<>();
            folderInfo.put("name", folder.getFullName());
            folderInfo.put("displayName", EmailFolderNameMapper.toDisplayName(folder.getFullName()));
            folderInfo.put("messageCount", String.valueOf(folder.getMessageCount()));
            folderList.add(folderInfo);
        }

        if ((folder.getType() & Folder.HOLDS_FOLDERS) != 0) {
            Folder[] subFolders = folder.list();
            for (Folder subFolder : subFolders) {
                getAllFoldersRecursive(subFolder, folderList);
            }
        }
    }

    /**
     * 获取指定文件夹的邮件列表
     */
    public Page<EmailMessages> getEmailsByFolder(Long accountId, String folderName, String keywords, int pageNumber,
                                                 int pageSize) {
        try {
            EmailAccount account = emailAccountService.findById(accountId);
            if (account == null) {
                return null;
            }

            Sql sql = selectSql().page(pageNumber, pageSize);

            Sql userInSql = Sql.me(this.dbType).from("emails_email").select("emails_id").eq("email",
                    account.getUsername());
            sql.inSql("id", userInSql);

            // 将显示用的中文名称转换为存储用的英文名称
            String storageFolder = EmailFolderNameMapper.toStorageName(folderName);
            sql.eq("folder_name", storageFolder);

            if (notOk(keywords)) {
                sql.likeMulti(keywords, "from_address", "subject", "to_address", "content");
            }
            sql.orderBy("sent_date", true);

            Page<EmailMessages> page = paginate(sql);

            // 处理显示名称
            if (page != null && page.getList() != null) {
                for (EmailMessages message : page.getList()) {
                    String fromAddress = message.getFromAddress();
                    if (fromAddress != null) {
                        String fromName = emailNameCache.getDisplayName(fromAddress);
                        message.put("from_name", fromName);
                    }
                }
            }

            return page;
        } catch (Exception e) {
            LogKit.error("获取邮件列表失败", e);
            return null;
        }
    }

    /**
     * 获取邮件详情
     */
    public Ret getEmailDetail(String emailId) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }

            // 添加发件人和收件人的显示名称
            String fromAddress = email.getFromAddress();
            if (fromAddress != null) {
                String fromName = emailNameCache.getDisplayName(fromAddress);
                email.put("from_name", fromName);
                email.put("fromName", fromName);
            }

            String toAddress = email.getToAddress();
            if (toAddress != null) {
                String toName = emailNameCache.getDisplayName(toAddress);
                email.put("to_name", toName);
                email.put("toName", toName);
            }

            return Ret.ok("data", email);
        } catch (Exception e) {
            LogKit.error("获取邮件详情失败", e);
            return fail("获取邮件详情失败：" + e.getMessage());
        }
    }

    public Ret getAttachments(String emailId) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }

            String contentHtml = noNull(email.getContentHtml());

            List<Map<String, Object>> attachments = new ArrayList<>();
            List<File> files = listEmailAttachments(email);
            System.out.println("===emailId: " + emailId + ", files:" + files);
            for (File file : files) {
                if (file.isFile()) {
                    // 检测文件类型
                    String mimeType = "";
                    String fileName = file.getName();
                    if (contentHtml.contains(fileName)) {
                        continue;
                    }
                    try {
                        mimeType = tika.detect(file);
                    } catch (Exception e) {
                        LogKit.warn("Failed to detect mime type for file: " + fileName, e);
                    }

                    Map<String, Object> attachment = new HashMap<>();
                    attachment.put("fileName", fileName);
                    attachment.put("size", FileUtil.readableFileSize(file.length()));
                    attachment.put("path", file.getAbsolutePath());
                    attachment.put("contentType", mimeType);
                    attachments.add(attachment);
                }
            }

            return Ret.ok("data", attachments);
        } catch (Exception e) {
            LogKit.error("获取附件列表失败", e);
            return fail("获取附件列表失败：" + e.getMessage());
        }
    }

    public Ret downloadAttachment(String emailId, String fileName) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }

            List<File> files = listEmailAttachments(email);
            for (File file : files) {
                if (fileName.equals(file.getName())) {
                    return Ret.ok("file", file)
                            .set("fileName", fileName);
                }
            }

            return fail("附件文件不存在");
        } catch (Exception e) {
            LogKit.error("获取附件失败", e);
            return fail("获取附件失败：" + e.getMessage());
        }
    }

    /**
     * 获取邮件的图片附件
     * @param emailId 邮件ID
     * @return 图片附件列表
     */
    public Ret getEmailImageAttachments(String emailId) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }

            List<Map<String, Object>> imageAttachments = new ArrayList<>();
            List<File> files = listEmailAttachments(email);
            
            for (File file : files) {
                if (file.isFile()) {
                    String fileName = file.getName();
                    String lowerFileName = fileName.toLowerCase();
                    
                    // 检查是否是图片文件
                    if (lowerFileName.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp|svg|tiff|ico|heic|heif)$")) {
                        String mimeType = "";
                        try {
                            mimeType = tika.detect(file);
                        } catch (Exception e) {
                            LogKit.warn("检测文件类型失败: " + fileName, e);
                        }
                        
                        // 如果MIME类型也表明是图片，或者无法检测MIME类型但文件名是图片扩展名
                        if (mimeType == null || mimeType.trim().isEmpty() || mimeType.startsWith("image/")) {
                            Map<String, Object> attachment = new HashMap<>();
                            attachment.put("filename", fileName);
                            attachment.put("name", fileName);
                            attachment.put("size", file.length());
                            attachment.put("path", file.getAbsolutePath());
                            attachment.put("contentType", mimeType);
                            attachment.put("isImage", true);
                            imageAttachments.add(attachment);
                        }
                    }
                }
            }

            return Ret.ok("data", imageAttachments);
        } catch (Exception e) {
            LogKit.error("获取邮件图片附件失败", e);
            return fail("获取邮件图片附件失败：" + e.getMessage());
        }
    }

    /**
     * 检查附件是否存在
     *
     * @param emailId  邮件ID
     * @param fileName 文件名
     * @return 检查结果
     */
    public Ret checkAttachmentExists(String emailId, String fileName) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }

            String attachPath = createAttachmentPath(email, null);
            // 记录搜索路径，方便调试
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("searchPath", attachPath);

            File attachDir = new File(attachPath);
            if (!attachDir.exists() || !attachDir.isDirectory()) {
                return fail("附件目录不存在").set("data", debugInfo);
            }

            List<File> files = listEmailAttachments(email);
            for (File file : files) {
                if (fileName.equals(file.getName())) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("fileName", file.getName());
                    fileInfo.put("filePath", file.getAbsolutePath());
                    fileInfo.put("fileSize", FileUtils.byteCountToDisplaySize(file.length()));
                    fileInfo.put("lastModified", new Date(file.lastModified()).toString());
                    fileInfo.put("canRead", file.canRead());
                    fileInfo.put("canWrite", file.canWrite());
                    fileInfo.put("isHidden", file.isHidden());

                    return Ret.ok("data", fileInfo);
                }
            }

            // 如果没有找到精确匹配，尝试列出所有文件名，可能是大小写问题
            List<String> allFileNames = new ArrayList<>();
            for (File file : files) {
                allFileNames.add(file.getName());
            }
            debugInfo.put("availableFiles", allFileNames);

            return fail("附件文件不存在").set("data", debugInfo);
        } catch (Exception e) {
            LogKit.error("检查附件是否存在失败", e);
            return fail("检查附件是否存在失败：" + e.getMessage());
        }
    }

    // 修改 downloadAllAttachments 方法中的压缩部分
    public Ret downloadAllAttachments(String emailId) {
        try {
            EmailMessages email = findById(emailId);
            if (email == null) {
                return fail("邮件不存在");
            }
            String attachPath = createAttachmentPath(email, null);

            File dir = new File(attachPath);
            if (!dir.exists() || !dir.isDirectory()) {
                return fail("附件目录不存在");
            }

            // 创建临时zip文件
            String zipFileName = "attachments_" + emailId + ".zip";
            String tempPath = PathKit.getWebRootPath() + "/temp";
            File tempDir = new File(tempPath);
            if (!tempDir.exists()) {
                FileUtils.forceMkdir(tempDir);
            }
            String zipFilePath = tempPath + "/" + zipFileName;

            // 使用 hutool 的 ZipUtil 进行压缩，使用正确的方法签名
            ZipUtil.zip(attachPath, zipFilePath);

            return Ret.ok("file", new File(zipFilePath))
                    .set("fileName", zipFileName);
        } catch (Exception e) {
            LogKit.error("打包附件失败", e);
            return fail("打包附件失败：" + e.getMessage());
        }
    }

    /**
     * 根据 messageId 查找邮件
     */
    public EmailMessages findByMessageId(String messageId) {
        return dao.findFirst("select * from email_messages where message_id = ?", messageId);
    }

    public Ret followUp(String id) {
        EmailMessages email = findById(id);
        if (email == null) {
            return fail("邮件不存在");
        }
        Integer isFollowUp = email.getIsFollowUp();
        if (isFollowUp == null || isFollowUp == 0) {
            email.setIsFollowUp(1);
            email.setFollowUpTime(new Date());
            email.setFollowUpId(JBoltUserKit.getUserId());
            email.setFollowType(1);
            email.update();
        } else {
            email.setIsFollowUp(0);
            email.setFollowUpTime(null);
            email.setFollowUpId(null);
            email.setFollowType(1);
            email.update();
        }

        return success("操作成功");
    }

    public Ret important(String id) {
        EmailMessages email = findById(id);
        if (email == null) {
            return fail("邮件不存在");
        }
        Integer isImportant = email.getIsImportant();
        if (isImportant == null || isImportant == 0) {
            email.setIsImportant(1);
            email.update();
        } else {
            email.setIsImportant(0);
            email.update();
        }

        return success("操作成功");
    }

    // 存储发送中的邮件信息，用于发送失败时保存草稿
    private static final ThreadLocal<Map<String, Object>> sendingEmailContext = new ThreadLocal<>();

    /**
     * 保存发送失败的邮件为草稿
     */
    private static String saveFailedEmailAsDraft(String messageId, String fromEmail, String toEmail, String ccEmail,
                                                 String subject, String content, Exception failureReason) {
        try {
            EmailMessages draft = new EmailMessages();
            draft.setMessageId(UUID.randomUUID().toString());
            draft.setFromAddress(fromEmail);
            draft.setToAddress(toEmail);
            if (ccEmail != null && !ccEmail.trim().isEmpty()) {
                draft.setCcAddress(ccEmail);
            }
            draft.setSentDate(new Date());
            draft.setSubject(subject);
            draft.setContentHtml(content);
            draft.setIsDraft(true);
            draft.setCreatedAt(new Date());
            draft.setUpdatedAt(new Date());

            // 添加失败原因到备注
            StringBuilder notes = new StringBuilder();
            notes.append("发送失败自动保存的草稿\n");
            notes.append("原始发送时间：").append(new Date()).append("\n");
            notes.append("失败原因：").append(failureReason != null ? failureReason.getMessage() : "未知错误");
            draft.setNotes(notes.toString());

            if (draft.save()) {
                log.info("邮件发送失败，已自动保存为草稿，draftId: {}, messageId: {}", draft.getId(), messageId);
                saveDraftToEmailsEmail(draft);
                return draft.getId().toString();
            } else {
                log.error("保存发送失败邮件草稿失败，messageId: {}", messageId);
                return null;
            }
        } catch (Exception e) {
            log.error("保存发送失败邮件草稿时出现异常，messageId: " + messageId, e);
            return null;
        }
    }

    /**
     * 保存草稿 - 向后兼容的方法
     */
    public Ret saveDraft(String fromEmail, String toEmail, String ccEmail, String subject,
                         String content, List<UploadFile> attachments, String originalEmailId, String draftId) {
        return saveDraft(fromEmail, toEmail, ccEmail, subject, content, attachments, originalEmailId, draftId, null);
    }

}