<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试客户链接功能</title>
    <link href="/assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 客户链接区域样式 */
        .client-links-row {
            margin-bottom: 15px;
        }
        
        .client-company-item {
            margin-bottom: 12px;
            padding: 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }
        
        .client-company-item:hover {
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        
        .client-company-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .client-company-name::before {
            content: "🏢";
            font-size: 16px;
        }
        
        .client-company-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .client-link-btn {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            border: none;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .client-link-btn:hover {
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .client-link-order {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        
        .client-link-document {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
        }
        
        .client-link-customer {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>客户链接功能测试</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>测试邮件信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label>发件人邮箱:</label>
                        <input type="text" id="fromAddress" class="form-control" value="<EMAIL>" placeholder="输入发件人邮箱">
                    </div>
                    <div class="col-md-4">
                        <label>收件人邮箱:</label>
                        <input type="text" id="toAddress" class="form-control" value="<EMAIL>" placeholder="输入收件人邮箱">
                    </div>
                    <div class="col-md-4">
                        <label>抄送人邮箱:</label>
                        <input type="text" id="ccAddress" class="form-control" value="<EMAIL>" placeholder="输入抄送人邮箱">
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="testClientLinks()">测试客户链接</button>
                    <button type="button" class="btn btn-secondary" onclick="clearResults()">清空结果</button>
                </div>
            </div>
        </div>
        
        <!-- 客户链接区域 -->
        <div class="client-links-row mt-4" id="clientLinksRow" style="display: none;">
            <div class="alert alert-info py-2">
                <div class="d-flex align-items-center mb-2">
                    <i class="fa fa-building text-primary me-2"></i>
                    <strong>相关客户公司：</strong>
                </div>
                <div id="clientLinksContent">
                    <!-- 客户链接将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 调试信息 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>调试信息</h5>
            </div>
            <div class="card-body">
                <pre id="debugInfo"></pre>
            </div>
        </div>
    </div>

    <script src="/assets/plugins/jquery/jquery.min.js"></script>
    <script src="/assets/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script>
        function testClientLinks() {
            const fromAddress = document.getElementById('fromAddress').value;
            const toAddress = document.getElementById('toAddress').value;
            const ccAddress = document.getElementById('ccAddress').value;
            
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.textContent = '正在测试客户链接功能...\n';
            debugInfo.textContent += `发件人: ${fromAddress}\n`;
            debugInfo.textContent += `收件人: ${toAddress}\n`;
            debugInfo.textContent += `抄送人: ${ccAddress}\n\n`;
            
            // 显示加载状态
            const clientLinksRow = document.getElementById('clientLinksRow');
            const clientLinksContent = document.getElementById('clientLinksContent');
            clientLinksContent.innerHTML = '<div class="text-center py-2"><i class="fa fa-spinner fa-spin"></i> 正在查找相关客户...</div>';
            clientLinksRow.style.display = 'block';
            
            $.ajax({
                url: '/admin/emailMessages/getCompaniesFromEmails',
                type: 'GET',
                data: {
                    fromAddress: fromAddress,
                    toAddress: toAddress,
                    ccAddress: ccAddress
                },
                dataType: 'json',
                success: function(response) {
                    debugInfo.textContent += '请求成功!\n';
                    debugInfo.textContent += '响应数据: ' + JSON.stringify(response, null, 2) + '\n';
                    
                    if (response.state === 'ok' && response.data && response.data.length > 0) {
                        displayClientLinks(response.data);
                        debugInfo.textContent += `找到 ${response.data.length} 个相关公司\n`;
                    } else {
                        clientLinksContent.innerHTML = '<div class="text-muted">未找到相关客户公司</div>';
                        debugInfo.textContent += '未找到相关客户公司\n';
                    }
                },
                error: function(xhr, status, error) {
                    debugInfo.textContent += '请求失败!\n';
                    debugInfo.textContent += `状态: ${status}\n`;
                    debugInfo.textContent += `错误: ${error}\n`;
                    debugInfo.textContent += `响应: ${xhr.responseText}\n`;
                    
                    clientLinksContent.innerHTML = '<div class="text-danger"><i class="fa fa-exclamation-triangle"></i> 查找客户信息失败</div>';
                }
            });
        }
        
        function displayClientLinks(companies) {
            const clientLinksContent = document.getElementById('clientLinksContent');
            
            let html = '';
            companies.forEach(function(company) {
                const companyName = escapeHtml(company.name || '未知公司');
                const nickName = escapeHtml(company.nick_name || '');
                const osclubId = company.osclub_id || '';
                
                html += '<div class="client-company-item">';
                html += '<div class="client-company-name">' + companyName + (nickName ? ' (' + nickName + ')' : '') + '</div>';
                html += '<div class="client-company-links">';
                
                let hasLinks = false;
                
                // 订单页面链接
                if (nickName) {
                    html += '<a href="http://360.theolympiastone.com/my/dd?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-order" title="查看订单信息">';
                    html += '<i class="fa fa-shopping-cart"></i>订单页面';
                    html += '</a>';
                    
                    // 单证页面链接
                    html += '<a href="http://360.theolympiastone.com/my/dzhy?query=' + encodeURIComponent(nickName) + '" target="_blank" class="client-link-btn client-link-document" title="查看单证信息">';
                    html += '<i class="fa fa-file-text"></i>单证页面';
                    html += '</a>';
                    
                    hasLinks = true;
                }
                
                // 客户页面链接
                if (osclubId) {
                    html += '<a href="http://360.theolympiastone.com/my/kh/edit?id=' + osclubId + '" target="_blank" class="client-link-btn client-link-customer" title="查看客户详情">';
                    html += '<i class="fa fa-user"></i>客户页面';
                    html += '</a>';
                    
                    hasLinks = true;
                }
                
                // 如果没有可用链接，显示提示
                if (!hasLinks) {
                    html += '<span class="text-muted small">暂无可用链接</span>';
                }
                
                html += '</div>';
                html += '</div>';
            });
            
            clientLinksContent.innerHTML = html;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function clearResults() {
            document.getElementById('clientLinksRow').style.display = 'none';
            document.getElementById('debugInfo').textContent = '';
        }
    </script>
</body>
</html>
