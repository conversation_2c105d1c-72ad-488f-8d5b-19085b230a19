-- 测试客户链接功能的SQL脚本

-- 1. 查看现有的公司数据
SELECT id, name, nick_name, osclub_id FROM company LIMIT 5;

-- 2. 查看现有的客户数据
SELECT id, name, email FROM client LIMIT 5;

-- 3. 查看公司客户关联关系
SELECT cc.id, c.name as company_name, c.nick_name, c.osclub_id, cl.name as client_name, cl.email
FROM company_client cc
LEFT JOIN company c ON cc.company_id = c.id
LEFT JOIN client cl ON cc.client_id = cl.id
LIMIT 5;

-- 4. 插入测试数据（如果没有现有数据）
-- 插入测试公司
INSERT IGNORE INTO company (id, name, nick_name, osclub_id, enable, create_time, update_time) 
VALUES 
(9999, '测试石材公司', 'TS', 123, 1, NOW(), NOW()),
(9998, '示例贸易公司', 'SM', 456, 1, NOW(), NOW());

-- 插入测试客户
INSERT IGNORE INTO client (id, name, email, enable, create_time, update_time) 
VALUES 
(9999, '张三', 'z<PERSON><PERSON>@example.com', 1, NOW(), NOW()),
(9998, '李四', '<EMAIL>', 1, NOW(), NOW()),
(9997, 'John Smith', '<EMAIL>', 1, NOW(), NOW());

-- 建立公司客户关联关系
INSERT IGNORE INTO company_client (company_id, client_id) 
VALUES 
(9999, 9999),  -- 测试石材公司 - 张三
(9999, 9998),  -- 测试石材公司 - 李四
(9998, 9997);  -- 示例贸易公司 - John Smith

-- 5. 测试查询语句（模拟后端接口的查询）
SELECT DISTINCT c.id, c.name, c.nick_name, c.osclub_id 
FROM company c 
INNER JOIN company_client cc ON c.id = cc.company_id 
INNER JOIN client cl ON cc.client_id = cl.id 
WHERE cl.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>') 
ORDER BY c.name;

-- 6. 清理测试数据（运行完测试后执行）
-- DELETE FROM company_client WHERE company_id IN (9999, 9998);
-- DELETE FROM client WHERE id IN (9999, 9998, 9997);
-- DELETE FROM company WHERE id IN (9999, 9998);
