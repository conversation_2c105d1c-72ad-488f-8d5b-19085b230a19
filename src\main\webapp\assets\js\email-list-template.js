/**
 * 邮件列表模板JavaScript库
 * 用于处理通用的邮件列表显示、排序、分页、批量操作等功能
 */
console.log('[调试] email-list-template.js 开始加载');

window.EmailListTemplate = (function () {
    console.log('[调试] EmailListTemplate IIFE 开始执行');

    // 存储各个实例的数据
    const instances = {};

    // 默认配置
    const defaultConfig = {
        showBatchActions: true,
        showPagination: true,
        enableColumnResize: true,
        pageSize: 100,
        sortField: 'sentdate',
        sortDirection: 'desc'
    };

    /**
     * 初始化邮件列表实例
     */
    function init(pageId, config = {}) {
        console.log('[初始化调试] 开始初始化实例:', pageId);
        console.log('[初始化调试] 传入的配置:', config);

        // 合并配置
        const instanceConfig = Object.assign({}, defaultConfig, config);
        console.log('[初始化调试] 合并后的配置:', instanceConfig);

        // 创建实例数据
        instances[pageId] = {
            config: instanceConfig,
            allEmails: [],
            filteredEmails: [],
            sortedEmails: [],
            pagination: {
                currentPage: 1,
                pageSize: instanceConfig.pageSize,
                totalPages: 0
            },
            sortState: {
                field: instanceConfig.sortField,
                direction: instanceConfig.sortDirection
            },
            resizeState: {
                isResizing: false,
                currentColumn: null,
                startX: 0,
                startWidth: 0
            }
        };

        console.log('[初始化调试] 实例数据创建完成:', instances[pageId]);

        // 检查分页相关的DOM元素是否存在
        setTimeout(() => {
            const topContainer = document.getElementById(`emailPaginationTopContainer_${pageId}`);
            const bottomContainer = document.getElementById(`emailPaginationBottomContainer_${pageId}`);
            console.log('[初始化调试] DOM元素检查:', {
                pageId,
                topContainer: !!topContainer,
                bottomContainer: !!bottomContainer,
                topContainerDisplay: topContainer ? topContainer.style.display : 'N/A',
                bottomContainerDisplay: bottomContainer ? bottomContainer.style.display : 'N/A'
            });
        }, 100);

        // 初始化功能
        if (instanceConfig.enableColumnResize) {
            initColumnResize(pageId);
        }

        console.log('[初始化调试] 实例初始化完成:', pageId);
    }

    /**
     * 渲染邮件列表（客户端分页）
     */
    function renderEmails(pageId, emails) {
        if (!instances[pageId]) {
            console.error('[EmailListTemplate] 实例不存在:', pageId);
            return;
        }

        console.log(`[EmailListTemplate] 开始渲染 ${emails.length} 封邮件，实例:`, pageId);

        const instance = instances[pageId];
        instance.allEmails = emails || [];

        // 应用排序
        applySorting(pageId);

        console.log('[EmailListTemplate] 邮件渲染完成');
    }

    /**
     * 渲染邮件列表（服务端分页）
     */
    function renderEmailsWithServerPagination(pageId, emails, paginationData = null) {
        if (!instances[pageId]) {
            console.error('[EmailListTemplate] 实例不存在:', pageId);
            return;
        }

        console.log(`[EmailListTemplate] 开始渲染 ${emails.length} 封邮件（服务端分页），实例:`, pageId);

        const instance = instances[pageId];
        instance.allEmails = emails || [];
        instance.sortedEmails = emails || [];

        // 如果传入了分页数据，设置到实例中
        if (paginationData) {
            instance.totalCount = paginationData.total;
            instance.pagination.currentPage = paginationData.currentPage;
            instance.pagination.totalPages = paginationData.totalPages;
            instance.pagination.pageSize = paginationData.pageSize || instance.pagination.pageSize;

            console.log('[EmailListTemplate] 设置服务端分页数据:', {
                totalCount: instance.totalCount,
                currentPage: instance.pagination.currentPage,
                totalPages: instance.pagination.totalPages,
                pageSize: instance.pagination.pageSize
            });
        }

        // 直接渲染当前页，不需要重新排序和分页
        renderDesktopTable(pageId, emails);
        renderMobileCards(pageId, emails);

        // 更新分页信息
        if (instance.config.showPagination) {
            updateServerPaginationInfo(pageId);
        }

        console.log('[EmailListTemplate] 邮件渲染完成（服务端分页）');
    }

    /**
     * 应用排序
     */
    function applySorting(pageId) {
        const instance = instances[pageId];
        if (!instance || !instance.allEmails.length) return;

        console.log(`[EmailListTemplate] 开始排序 ${instance.allEmails.length} 封邮件`);

        const sortedEmails = sortEmails(instance.allEmails, instance.sortState.field, instance.sortState.direction);
        instance.sortedEmails = sortedEmails;
        instance.pagination.totalPages = Math.ceil(sortedEmails.length / instance.pagination.pageSize);
        instance.pagination.currentPage = 1;

        renderCurrentPage(pageId);
        updateSortIcons(pageId);
        if (instance.config.showPagination) {
            updatePaginationInfo(pageId);
        }
    }

    /**
     * 邮件排序函数
     */
    function sortEmails(emails, field, direction) {
        return emails.slice().sort((a, b) => {
            let aVal = a[field] || '';
            let bVal = b[field] || '';

            // 特殊处理日期字段
            if (field === 'sentdate') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            let result = 0;
            if (aVal < bVal) result = -1;
            else if (aVal > bVal) result = 1;

            return direction === 'desc' ? -result : result;
        });
    }

    /**
     * 渲染当前页邮件
     */
    function renderCurrentPage(pageId) {
        const instance = instances[pageId];
        if (!instance) return;

        const {sortedEmails, pagination} = instance;
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
        const endIndex = Math.min(startIndex + pagination.pageSize, sortedEmails.length);
        const pageEmails = sortedEmails.slice(startIndex, endIndex);

        renderDesktopTable(pageId, pageEmails);
        renderMobileCards(pageId, pageEmails);
    }

    /**
     * 渲染桌面版表格
     */
    function renderDesktopTable(pageId, emails) {
        const tbody = document.getElementById(`email_tbody_${pageId}`);
        console.log(`[EmailListTemplate] 查找tbody元素 - ID: email_tbody_${pageId}, 找到: ${!!tbody}`);
        if (!tbody) {
            console.error(`[EmailListTemplate] tbody元素不存在，ID: email_tbody_${pageId}`);
            // 列出所有可能相关的元素
            const allTbodies = document.querySelectorAll('tbody');
            console.log(`[EmailListTemplate] 页面上所有tbody元素:`, allTbodies);
            return;
        }

        if (emails.length === 0) {
            const colCount = instances[pageId].config.showBatchActions ? 9 : 8;
            tbody.innerHTML = `
                <tr>
                    <td colspan="${colCount}" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fa fa-inbox fa-3x mb-3"></i>
                            <p>暂无邮件</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        let html = '';
        emails.forEach(email => {
            html += renderEmailRow(pageId, email);
        });

        tbody.innerHTML = html;
        console.log(`[EmailListTemplate] ✓ 桌面版表格渲染完成 - 渲染了 ${emails.length} 封邮件`);

        // 重新初始化工具提示
        $('[data-toggle="tooltip"]').tooltip();
    }

    /**
     * 渲染邮件行
     */
    function renderEmailRow(pageId, email) {
        const showBatch = instances[pageId].config.showBatchActions;

        // 邮件类型判断逻辑：草稿 > 已发送 > 已接收
        const isDraft = email.is_draft || false;
        const isSent = email.is_sent || false;

        // 使用badge样式的邮件类型标签
        let emailTypeTag;
        if (isDraft) {
            emailTypeTag = '<span class="badge badge-warning mr-1">草</span>';
        } else if (isSent) {
            emailTypeTag = '<span class="badge badge-success mr-1">发</span>';
        } else {
            emailTypeTag = '<span class="badge badge-danger mr-1">收</span>';
        }

        // 处理基础数据
        const subject = escapeHtml(email.subject || '(无主题)');
        const sentDate = escapeHtml(email.sentdate || '');

        // 使用增强的地址渲染功能
        const fromHtml = renderAddressesAsHTML(
            email.from_address, email.fromname, 'from'
        );
        const toHtml = renderAddressesAsHTML(
            email.to_address, email.toname, 'to'
        );

        // 邮件预览处理
        const contentPreview = getEmailContentPreview(email.content_html || '', email.content_text || '');
        const previewText = contentPreview ? escapeHtml(contentPreview) : '(无内容预览)';
        const previewClass = contentPreview ? '' : 'empty';

        // 未读状态处理
        const unreadClass = email.is_read ? '' : 'unread';

        return `
            <tr class="${unreadClass}" data-email-id="${email.id}">
                ${showBatch ? `
                <td class="px-1 py-1 text-center" onclick="event.stopPropagation();">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input email-checkbox" id="email_${pageId}_${email.id}" value="${email.id}" onchange="EmailListTemplate.updateBatchButtons('${pageId}')">
                        <label class="custom-control-label" for="email_${pageId}_${email.id}"></label>
                    </div>
                </td>
                ` : ''}
                <td class="px-2 py-1" title="${email.from_address}">${fromHtml}</td>
                <td class="px-2 py-1" title="${email.to_address}">${toHtml}</td>
                <td class="px-2 py-1" title="${sentDate}">${sentDate}</td>
                <td class="px-2 py-1" title="${subject}" onclick="EmailListTemplate.showEmailDetail('${email.id}')">
                    ${emailTypeTag}${email.has_attachments ? '<i class="fa fa-paperclip mr-1"></i> ' : ''}${subject}
                </td>
                <td class="px-2 py-1 text-center" onclick="event.stopPropagation();">
                    ${renderTrackingInfo(email)}
                </td>
                <td class="px-2 py-1 email-preview-cell ${previewClass}" title="${previewText}" onclick="EmailListTemplate.showEmailDetail('${email.id}')">${previewText}</td>
                <td class="px-1 py-1 text-center email-actions-cell">
                    ${renderEmailActions(email)}
                </td>
            </tr>
        `;
    }

    /**
     * 渲染邮件操作按钮
     */
    function renderEmailActions(email) {
        return `
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.toggleReadStatus('${email.id}')" data-toggle="tooltip" title="${email.is_read ? '标记为未读' : '标记为已读'}">
                <i class="fa ${email.is_read ? 'fa-envelope-open' : 'fa-envelope'} ${email.is_read ? 'text-muted' : 'text-primary'}"></i>
            </button>
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.toggleFollowUp('${email.id}')" data-toggle="tooltip" title="${(email.is_follow_up && email.follow_type==1) ? '处理人：' + (email.follow_up_user_name || '未知') + '，时间：' + (email.follow_up_time || '未知') : '标记为处理'}">
                <span style="color: red;">${(email.is_follow_up || email.follow_type==0) ? '' : '未处理'}</span>
            </button>
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.toggleImportant('${email.id}')" data-toggle="tooltip" title="${email.is_important ? '取消重要' : '标记重要'}">
                <i class="fa ${email.is_important ? 'fa-star' : 'fa-star-o'} ${email.is_important ? 'text-warning' : 'text-muted'}"></i>
            </button>
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.replyEmail('${email.id}')" data-toggle="tooltip" title="回复邮件">
                <i class="fa fa-reply text-primary"></i>
            </button>
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.translateEmail('${email.id}')" data-toggle="tooltip" title="翻译邮件">
                <i class="fa fa-language text-info"></i>
            </button>
            <button class="btn btn-sm btn-link p-0 email-action-btn" onclick="event.stopPropagation();EmailListTemplate.deleteEmail('${email.id}')" data-toggle="tooltip" title="删除邮件">
                <i class="fa fa-trash-o text-danger"></i>
            </button>
        `;
    }

    /**
     * 渲染跟踪信息
     */
    function renderTrackingInfo(email) {
        // 只对已发送的邮件显示跟踪信息
        if (!email.is_sent) {
            return '<span class="text-muted">-</span>';
        }

        // 如果有跟踪数据
        if (email.tracking_data) {
            const tracking = email.tracking_data;
            let html = '';

            // 投递状态
            if (tracking.delivery_status === 2) {
                html += '<i class="fa fa-check-circle text-success" title="投递成功"></i> ';
            } else if (tracking.delivery_status === 3 || tracking.delivery_status === 4) {
                html += '<i class="fa fa-times-circle text-danger" title="投递失败"></i> ';
            } else {
                html += '<i class="fa fa-clock text-warning" title="已发送"></i> ';
            }

            // 打开状态
            if (tracking.unique_opens > 0) {
                html += `<span class="badge badge-success" title="已打开 ${tracking.unique_opens} 次">${tracking.unique_opens}</span>`;
            } else {
                html += '<span class="badge badge-secondary" title="未打开">0</span>';
            }

            // 点击查看详情
            html = `<div class="tracking-info" onclick="EmailListTemplate.showTrackingDetail('${email.id}')" style="cursor: pointer;" title="点击查看跟踪详情">${html}</div>`;

            return html;
        }

        // 没有跟踪数据，显示加载按钮
        return `<button class="btn btn-sm btn-outline-info" onclick="EmailListTemplate.loadTrackingInfo('${email.id}')" title="加载跟踪信息">
                    <i class="fa fa-search"></i>
                </button>`;
    }

    /**
     * 渲染移动端卡片
     */
    function renderMobileCards(pageId, emails) {
        const container = document.getElementById(`mobile_email_list_${pageId}`);
        if (!container) return;

        if (emails.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-muted">
                        <i class="fa fa-inbox fa-3x mb-3"></i>
                        <p>暂无邮件</p>
                    </div>
                </div>
            `;
            return;
        }

        let html = '';
        emails.forEach(email => {
            html += renderMobileCard(pageId, email);
        });

        container.innerHTML = html;
    }

    /**
     * 渲染移动端邮件卡片
     */
    function renderMobileCard(pageId, email) {
        const showBatch = instances[pageId].config.showBatchActions;

        // 邮件类型判断逻辑：草稿 > 已发送 > 已接收
        const isDraft = email.is_draft || false;
        const isSent = email.is_sent || false;

        let emailTypeText, emailTypeClass;
        if (isDraft) {
            emailTypeText = "草稿";
            emailTypeClass = "draft";
        } else if (isSent) {
            emailTypeText = "发出";
            emailTypeClass = "outgoing";
        } else {
            emailTypeText = "收到";
            emailTypeClass = "incoming";
        }

        const subject = escapeHtml(email.subject || '(无主题)');
        const sentDate = escapeHtml(email.sentdate || '');

        // 使用增强的地址渲染功能
        const fromHtml = renderAddressesAsHTML(
            email.from_address, email.fromname, 'from'
        );

        const contentPreview = getEmailContentPreview(email.content_html || '', email.content_text || '', 80);
        const previewText = contentPreview ? escapeHtml(contentPreview) : '(无内容预览)';

        return `
            <div class="mobile-email-card ${email.is_read ? '' : 'unread'}" data-email-id="${email.id}" onclick="EmailListTemplate.showEmailDetail('${email.id}')">
                <div class="mobile-email-header">
                    ${showBatch ? `
                    <div class="mobile-email-checkbox" onclick="event.stopPropagation();">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input email-checkbox" id="mobile_email_${pageId}_${email.id}" value="${email.id}" onchange="EmailListTemplate.updateBatchButtons('${pageId}')">
                            <label class="custom-control-label" for="mobile_email_${pageId}_${email.id}"></label>
                        </div>
                    </div>
                    ` : ''}
                    <div class="mobile-email-from">${fromHtml}</div>
                    <div class="mobile-email-time">${sentDate}</div>
                </div>
                <div class="mobile-email-subject">
                    ${email.has_attachments ? '<i class="fa fa-paperclip mr-1"></i>' : ''}${subject}
                </div>
                <div class="mobile-email-preview">${previewText}</div>
                <div class="mobile-email-meta">
                    <div class="mobile-email-type">
                        <span class="mobile-email-badge ${emailTypeClass}">${emailTypeText}</span>
                        ${email.is_follow_up ? '<i class="fa fa-flag text-danger" title="已处理"></i>' : ''}
                    </div>
                    <div class="mobile-email-actions">
                        ${renderMobileActions(email)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染移动端操作按钮
     */
    function renderMobileActions(email) {
        return `
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.toggleReadStatus('${email.id}')" title="${email.is_read ? '标记为未读' : '标记为已读'}">
                <i class="fa ${email.is_read ? 'fa-envelope-open' : 'fa-envelope'}"></i>
            </button>
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.toggleFollowUp('${email.id}')" title="${email.is_follow_up ? '取消处理' : '标记处理'}">
                <i class="fa ${email.is_follow_up ? 'fa-flag-o' : 'fa-flag'}"></i>
            </button>
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.toggleImportant('${email.id}')" title="${email.is_important ? '取消重要' : '标记重要'}">
                <i class="fa ${email.is_important ? 'fa-star' : 'fa-star-o'}"></i>
            </button>
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.replyEmail('${email.id}')" title="回复">
                <i class="fa fa-reply"></i>
            </button>
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.translateEmail('${email.id}')" title="翻译">
                <i class="fa fa-language"></i>
            </button>
            <button class="btn" onclick="event.stopPropagation();EmailListTemplate.deleteEmail('${email.id}')" title="删除">
                <i class="fa fa-trash-o"></i>
            </button>
        `;
    }

    // 工具函数
    function escapeHtml(text) {
        if (typeof JBoltEmailUtil !== 'undefined' && JBoltEmailUtil.escapeHtml) {
            return JBoltEmailUtil.escapeHtml(text);
        }
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getEmailContentPreview(htmlContent, textContent, maxLength = 100) {
        if (typeof JBoltEmailUtil !== 'undefined' && JBoltEmailUtil.getEmailContentPreview) {
            return JBoltEmailUtil.getEmailContentPreview(htmlContent, textContent, maxLength);
        }
        // 简化版本的内容预览提取
        let content = textContent || htmlContent || '';
        content = content.replace(/<[^>]*>/g, '').trim();
        return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
    }

    /**
     * 渲染邮件地址为可点击的HTML元素（集成从renderDesktopRow的增强功能）
     * @param {string} addressString - 邮件地址字符串
     * @param {string} nameString - 名称字符串
     * @param {string} type - 地址类型，'from' 或 'to'
     * @returns {string} 渲染后的HTML字符串
     */
    function renderAddressesAsHTML(addressString, nameString, type = 'from') {
        // 优先使用JBoltEmailAddressUtil，如果不存在则使用简化版本
        if (typeof JBoltEmailAddressUtil !== 'undefined') {
            try {
                const util = new JBoltEmailAddressUtil();
                return util.renderAddressesAsHTML(addressString, nameString, type);
            } catch (error) {
                console.warn('[EmailListTemplate] JBoltEmailAddressUtil渲染失败，使用简化版本:', error);
            }
        }

        // 简化版本的地址渲染
        return renderSimpleAddress(addressString, nameString, type);
    }

    /**
     * 简化版本的地址渲染（不依赖JBoltEmailAddressUtil）
     * @param {string} addressString - 邮件地址字符串
     * @param {string} nameString - 名称字符串
     * @param {string} type - 地址类型
     * @returns {string} HTML字符串
     */
    function renderSimpleAddress(addressString, nameString, type) {
        if (!addressString) {
            return '<span class="text-muted">(无地址)</span>';
        }

        // 处理多个地址（简化版本）
        const addresses = addressString.split(/[,;]/).map(s => s.trim()).filter(s => s.length > 0);
        const names = nameString ? nameString.split(/[,;]/).map(s => s.trim()) : [];

        if (addresses.length === 0) {
            return '<span class="text-muted">(无地址)</span>';
        }

        // 获取第一个地址的显示名称
        const firstAddress = addresses[0];
        const firstName = names[0] || extractNameFromAddress(firstAddress);
        const displayName = escapeHtml(firstName || firstAddress);

        // 如果只有一个地址，直接显示
        if (addresses.length === 1) {
            return `<span class="email-address-link" 
                          title="${escapeHtml(displayName)} - 点击查看往来邮件"
                          onclick="event.stopPropagation(); EmailListTemplate.showEmailHistory('${escapeHtml(firstAddress)}', '${escapeHtml(firstName)}', '${type}')"
                          style="cursor: pointer; color: #007bff;">
                        ${displayName}
                    </span>`;
        }

        // 多个地址，显示第一个+数量
        const remainingCount = addresses.length - 1;
        return `<span class="email-address-link" 
                      title="${escapeHtml(displayName)} - 点击查看往来邮件"
                      onclick="event.stopPropagation(); EmailListTemplate.showEmailHistory('${escapeHtml(firstAddress)}', '${escapeHtml(firstName)}', '${type}')"
                      style="cursor: pointer; color: #007bff;">
                    ${displayName}
                </span>
                <span class="email-address-expand" 
                      title="点击查看全部 ${addresses.length} 个地址"
                      onclick="event.stopPropagation(); EmailListTemplate.showAllAddresses('${escapeHtml(addressString)}', '${escapeHtml(nameString)}', '${type}')"
                      style="cursor: pointer; color: #6c757d; margin-left: 4px;">
                    +${remainingCount}
                </span>`;
    }

    /**
     * 从邮件地址中提取显示名称
     * @param {string} address - 邮件地址
     * @returns {string} 提取的名称
     */
    function extractNameFromAddress(address) {
        if (!address || typeof address !== 'string') return '';

        // 匹配邮箱地址中的显示名称部分
        const match = address.match(/^(.+?)\s*<.*>$/);
        if (match) {
            return match[1].replace(/['"]/g, '').trim();
        }

        // 如果没有显示名称，尝试从邮箱地址中提取
        const atIndex = address.indexOf('@');
        if (atIndex > 0) {
            return address.substring(0, atIndex);
        }

        return address;
    }

    /**
     * 加载邮件跟踪信息
     */
    function loadTrackingInfo(emailId) {
        $.ajax({
            url: '/admin/emailMessages/getEmailTracking',
            type: 'GET',
            data: { emailMessageId: emailId },
            success: function(response) {
                if (response.state === 'ok' && response.data) {
                    // 更新页面上的跟踪信息显示
                    updateTrackingDisplay(emailId, response.data);
                } else {
                    console.log('未找到跟踪信息:', response.msg);
                }
            },
            error: function() {
                console.error('加载跟踪信息失败');
            }
        });
    }

    /**
     * 更新跟踪信息显示
     */
    function updateTrackingDisplay(emailId, trackingData) {
        const trackingCells = document.querySelectorAll(`tr[data-email-id="${emailId}"] .tracking-info`);
        trackingCells.forEach(cell => {
            const record = trackingData.record;
            let html = '';

            // 投递状态
            if (record.deliveryStatus === 2) {
                html += '<i class="fa fa-check-circle text-success" title="投递成功"></i> ';
            } else if (record.deliveryStatus === 3 || record.deliveryStatus === 4) {
                html += '<i class="fa fa-times-circle text-danger" title="投递失败"></i> ';
            } else {
                html += '<i class="fa fa-clock text-warning" title="已发送"></i> ';
            }

            // 打开状态
            if (record.uniqueOpens > 0) {
                html += `<span class="badge badge-success" title="已打开 ${record.uniqueOpens} 次">${record.uniqueOpens}</span>`;
            } else {
                html += '<span class="badge badge-secondary" title="未打开">0</span>';
            }

            cell.innerHTML = html;
            cell.onclick = function() { showTrackingDetail(emailId, trackingData); };
        });
    }

    /**
     * 显示跟踪详情
     */
    function showTrackingDetail(emailId, trackingData = null) {
        if (trackingData) {
            renderTrackingDetailModal(trackingData);
        } else {
            // 重新加载跟踪数据
            $.ajax({
                url: '/admin/emailMessages/getEmailTracking',
                type: 'GET',
                data: { emailMessageId: emailId },
                success: function(response) {
                    if (response.state === 'ok' && response.data) {
                        renderTrackingDetailModal(response.data);
                    } else {
                        LayerMsgBox.alert('未找到跟踪信息');
                    }
                },
                error: function() {
                    LayerMsgBox.alert('加载跟踪信息失败');
                }
            });
        }
    }

    /**
     * 渲染跟踪详情模态框
     */
    function renderTrackingDetailModal(data) {
        const record = data.record;
        const opens = data.opens || [];

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h5>基本信息</h5>
                    <table class="table table-sm">
                        <tr><td>跟踪ID:</td><td>${record.trackingId || ''}</td></tr>
                        <tr><td>主题:</td><td>${record.subject || ''}</td></tr>
                        <tr><td>发件人:</td><td>${record.fromAddress || ''}</td></tr>
                        <tr><td>收件人:</td><td>${record.toAddress || ''}</td></tr>
                        <tr><td>发送时间:</td><td>${record.sentDateText || ''}</td></tr>
                        <tr><td>投递状态:</td><td>${record.deliveryStatusText || ''}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5>跟踪统计</h5>
                    <table class="table table-sm">
                        <tr><td>总打开次数:</td><td>${record.totalOpens || 0}</td></tr>
                        <tr><td>唯一打开次数:</td><td>${record.uniqueOpens || 0}</td></tr>
                        <tr><td>首次打开时间:</td><td>${record.firstOpenDateText || '未打开'}</td></tr>
                        <tr><td>最后打开时间:</td><td>${record.lastOpenDateText || '未打开'}</td></tr>
                        <tr><td>打开率:</td><td>${record.openRate ? record.openRate.toFixed(2) + '%' : '0%'}</td></tr>
                    </table>
                </div>
            </div>
        `;

        if (opens.length > 0) {
            html += `
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h5>打开记录</h5>
                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                            <table class="table table-sm table-striped">
                                <thead><tr><th>打开时间</th><th>IP地址</th><th>设备类型</th><th>邮件客户端</th><th>位置</th></tr></thead>
                                <tbody>
            `;
            opens.forEach(function(open) {
                html += `
                    <tr>
                        <td>${open.openDateText || ''}</td>
                        <td>${open.maskedIpAddress || ''}</td>
                        <td>${open.deviceTypeText || ''}</td>
                        <td>${open.emailClientText || ''}</td>
                        <td>${open.fullLocation || ''}</td>
                    </tr>
                `;
            });
            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        layer.open({
            type: 1,
            title: '邮件跟踪详情',
            area: ['800px', '600px'],
            content: html,
            maxmin: true
        });
    }

    // 对外暴露的API
    return {
        init: init,
        renderEmails: renderEmails,
        renderEmailsWithServerPagination: renderEmailsWithServerPagination,

        // 设置自定义回调方法
        setCustomCallbacks: function (pageId, callbacks) {
            const instance = instances[pageId];
            if (!instance) {
                console.error('[EmailListTemplate] 实例不存在:', pageId);
                return;
            }

            // 存储自定义回调到实例配置中
            instance.config.customCallbacks = callbacks || {};
            instance.callbacks = callbacks || {};  // 同时设置到instance.callbacks中
            console.log('[EmailListTemplate] 自定义回调已设置:', pageId, callbacks);
        },

        // 加载数据方法
        loadData: function (pageId, params = {}) {
            const instance = instances[pageId];
            if (!instance) {
                console.error('[EmailListTemplate] 实例不存在:', pageId);
                return;
            }

            const apiUrl = instance.config.apiUrl;
            if (!apiUrl) {
                console.error('[EmailListTemplate] 未配置API地址');
                return;
            }

            // 合并参数
            const requestParams = Object.assign({}, instance.config.defaultParams || {}, params);

            console.log('[EmailListTemplate] 开始加载数据:', requestParams);

            // 显示加载状态
            showLoadingState(pageId);

            // 发送请求
            $.ajax({
                url: apiUrl,
                type: 'GET',
                data: requestParams,
                dataType: 'json',
                success: function (response) {
                    console.log('[EmailListTemplate] 数据加载成功:', response);

                    if (response.state === 'ok' && response.data && response.data.list) {
                        // 使用服务端返回的分页信息
                        if (response.data.total !== undefined) {
                            instance.pagination.currentPage = response.data.currentPage || 1;
                            instance.pagination.pageSize = response.data.pageSize || instance.pagination.pageSize;
                            instance.pagination.totalPages = response.data.totalPages || 1;
                            instance.totalCount = response.data.total;
                            console.log('[EmailListTemplate] 使用服务端分页信息:', {
                                total: instance.totalCount,
                                currentPage: instance.pagination.currentPage,
                                totalPages: instance.pagination.totalPages
                            });
                        }
                        renderEmailsWithServerPagination(pageId, response.data.list);
                        
                        // 调用数据加载完成回调
                        if (instance.callbacks && instance.callbacks.onDataLoaded) {
                            instance.callbacks.onDataLoaded(response);
                        }
                    } else {
                        console.error('[EmailListTemplate] 响应格式错误:', response);
                        showErrorState(pageId, '数据格式错误');
                    }
                },
                error: function (xhr, status, error) {
                    console.error('[EmailListTemplate] 数据加载失败:', error);
                    showErrorState(pageId, '加载失败：' + error);
                }
            });
        },

        handleSort: function (pageId, field) {
            const instance = instances[pageId];
            if (!instance) return;

            if (instance.sortState.field === field) {
                instance.sortState.direction = instance.sortState.direction === 'asc' ? 'desc' : 'asc';
            } else {
                instance.sortState.field = field;
                instance.sortState.direction = field === 'sentdate' ? 'desc' : 'asc';
            }

            applySorting(pageId);
        },
        changePage: function (pageId, page) {
            const instance = instances[pageId];
            if (!instance || page < 1 || page > instance.pagination.totalPages) return;

            instance.pagination.currentPage = page;
            renderCurrentPage(pageId);
            updatePaginationInfo(pageId);
        },
        changeServerPage: function (pageId, page) {
            const instance = instances[pageId];
            if (!instance || page < 1 || page > instance.pagination.totalPages) return;

            console.log('[EmailListTemplate] 切换到服务端分页第', page, '页 - 转发到dashboard分页函数');

            // 使用dashboard的分页函数
            if (typeof window.changeDashboardPage === 'function') {
                window.changeDashboardPage(page);
            } else {
                console.warn('[EmailListTemplate] dashboard分页函数不存在，使用默认逻辑');
                // 降级处理：使用内置逻辑
                const defaultParams = instance.config.defaultParams || {};
                const requestParams = Object.assign({}, defaultParams, { page: page });
                this.loadData(pageId, requestParams);
            }
        },
        changePageSize: function (pageId) {
            // 尝试从顶部或底部选择器获取值
            const topSelector = document.getElementById(`pageSizeSelectorTop_${pageId}`);
            const bottomSelector = document.getElementById(`pageSizeSelectorBottom_${pageId}`);
            const instance = instances[pageId];

            if (!instance) return;

            // 获取新的页面大小值
            let newPageSize;
            if (topSelector && topSelector.value) {
                newPageSize = parseInt(topSelector.value);
            } else if (bottomSelector && bottomSelector.value) {
                newPageSize = parseInt(bottomSelector.value);
            } else {
                return;
            }

            if (isNaN(newPageSize) || newPageSize <= 0) return;

            console.log(`[EmailListTemplate] 改变页面大小: ${instance.pagination.pageSize} -> ${newPageSize}`);

            // 同步两个选择器的值
            if (topSelector) topSelector.value = newPageSize;
            if (bottomSelector) bottomSelector.value = newPageSize;

            // 判断是否使用服务端分页
            if (instance.totalCount !== undefined) {
                // 服务端分页：使用dashboard的分页逻辑
                console.log('[EmailListTemplate] 服务端分页 - 切换每页大小到', newPageSize);

                // 调用dashboard的分页函数
                if (typeof window.changeDashboardPageSize === 'function') {
                    window.changeDashboardPageSize(newPageSize);
                } else {
                    console.warn('[EmailListTemplate] dashboard分页函数不存在，使用默认逻辑');
                    // 如果dashboard函数不存在，使用内置逻辑
                    const defaultParams = instance.config.defaultParams || {};
                    const requestParams = Object.assign({}, defaultParams, {
                        page: 1,
                        pageSize: newPageSize
                    });
                    this.loadData(pageId, requestParams);
                }
            } else {
                // 客户端分页：本地处理
                instance.pagination.pageSize = newPageSize;
                instance.pagination.currentPage = 1;
                instance.pagination.totalPages = Math.ceil(instance.sortedEmails.length / instance.pagination.pageSize);

                renderCurrentPage(pageId);
                updatePaginationInfo(pageId);
            }
        },

        // 邮件操作方法（需要在具体页面中实现）
        showEmailDetail: function (emailId) {
            console.log('[EmailListTemplate] 查看邮件详情:', emailId);

            // 查找包含此邮件的实例
            let targetInstance = null;
            let targetPageId = null;

            for (const pageId in instances) {
                const instance = instances[pageId];
                if (instance && instance.config.customCallbacks && instance.config.customCallbacks.onEmailClick) {
                    targetInstance = instance;
                    targetPageId = pageId;
                    break;
                }
            }

            // 如果找到自定义回调，使用它
            if (targetInstance && targetInstance.config.customCallbacks.onEmailClick) {
                console.log('[EmailListTemplate] 使用自定义邮件点击回调:', targetPageId);
                targetInstance.config.customCallbacks.onEmailClick(emailId, null);
                return;
            }

            // 使用统一的判断逻辑
            this.handleEmailAction(emailId, 'view', function(url) {
                if (url && url.includes('showTranslation')) {
                    // 如果返回翻译页面URL，使用layer弹窗打开
                    const winWidth = window.innerWidth * 0.9;
                    const winHeight = window.innerHeight * 0.9;

                    layer.open({
                        type: 2,
                        title: '邮件翻译',
                        area: [winWidth + 'px', winHeight + 'px'],
                        maxmin: true,
                        content: url,
                        success: function (layero, index) {
                            $(layero).find('.layui-layer-content').css({
                                'overflow': 'auto',
                                'height': (winHeight - 50) + 'px'
                            });
                        }
                    });
                } else {
                    // 否则打开邮件详情页面
                    const detailUrl = `admin/emailMessages/viewEmail/${emailId}?_jb_rqtype_=dialog`;
                    console.log('Detail URL:', detailUrl);
                    window.open(detailUrl, '_blank');
                }
            });
        },
        toggleReadStatus: function (emailId) {
            console.log('[EmailListTemplate] 切换阅读状态:', emailId);
            if (window.toggleReadStatus && typeof window.toggleReadStatus === 'function') {
                window.toggleReadStatus(emailId);
                return;
            }

            // 显示加载状态
            const loadingIndex = layer.msg('正在更新...', {
                icon: 16,
                shade: 0.1,
                time: 0
            });

            Ajax.post('admin/emailMessages/toggleEmailReadStatus', {
                id: emailId
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    // Ajax方式更新邮件状态，不刷新页面
                    EmailListTemplate.updateEmailStatusInList(emailId, 'read', res.data);
                    LayerMsgBox.success(res.msg || '状态更新成功');
                } else {
                    LayerMsgBox.alert(res.msg || '操作失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('网络错误，请稍后重试');
            });
        },
        toggleFollowUp: function (emailId) {
            console.log('[EmailListTemplate] 切换处理状态:', emailId);
            if (window.toggleFollowUp && typeof window.toggleFollowUp === 'function') {
                window.toggleFollowUp(emailId);
                return;
            }

            // 显示加载状态
            const loadingIndex = layer.msg('正在更新...', {
                icon: 16,
                shade: 0.1,
                time: 0
            });

            Ajax.post('admin/emailMessages/followUp', {
                id: emailId
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    // Ajax方式更新邮件状态，不刷新页面
                    EmailListTemplate.updateEmailStatusInList(emailId, 'followUp', res.data);
                    LayerMsgBox.success(res.msg || '状态更新成功');
                } else {
                    LayerMsgBox.alert(res.msg || '操作失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('网络错误，请稍后重试');
            });
        },
        toggleImportant: function (emailId) {
            console.log('[EmailListTemplate] 切换重要状态:', emailId);
            if (window.toggleImportant && typeof window.toggleImportant === 'function') {
                window.toggleImportant(emailId);
                return;
            }

            // 显示加载状态
            const loadingIndex = layer.msg('正在更新...', {
                icon: 16,
                shade: 0.1,
                time: 0
            });

            Ajax.post('admin/emailMessages/important', {
                id: emailId
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    // Ajax方式更新邮件状态，不刷新页面
                    EmailListTemplate.updateEmailStatusInList(emailId, 'important', res.data);
                    LayerMsgBox.success(res.msg || '状态更新成功');
                } else {
                    LayerMsgBox.alert(res.msg || '操作失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('网络错误，请稍后重试');
            });
        },
        replyEmail: function (emailId) {
            console.log('[EmailListTemplate] 回复邮件:', emailId);
            if (window.replyEmail && typeof window.replyEmail === 'function') {
                window.replyEmail(emailId);
                return;
            }

            // 调用统一的判断方法
            this.handleEmailAction(emailId, 'reply', function(url) {
                window.open(url, '_blank');
            });
        },
        translateEmail: function (emailId) {
            console.log('[EmailListTemplate] 翻译邮件:', emailId);
            if (window.translateEmail && typeof window.translateEmail === 'function') {
                window.translateEmail(emailId);
                return;
            }

            // 调用统一的判断方法
            this.handleEmailAction(emailId, 'translate', function(url) {
                const loadingIndex = layer.msg('正在准备翻译...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 获取窗口尺寸，用于设置弹窗大小
                const winWidth = window.innerWidth * 0.9;  // 窗口宽度的90%
                const winHeight = window.innerHeight * 0.9;  // 窗口高度的90%

                // 使用layer弹窗打开翻译页面
                layer.close(loadingIndex);
                layer.open({
                    type: 2,
                    title: '邮件翻译',
                    area: [winWidth + 'px', winHeight + 'px'],
                    maxmin: true,  // 允许最大化和最小化
                    content: url,
                    success: function (layero, index) {
                        // 确保弹窗内容区域高度合适
                        $(layero).find('.layui-layer-content').css({
                            'overflow': 'auto',
                            'height': (winHeight - 50) + 'px'  // 减去标题栏高度
                        });
                    }
                });
            });
        },
        deleteEmail: function (emailId) {
            console.log('[EmailListTemplate] 删除邮件:', emailId);
            if (window.deleteEmail && typeof window.deleteEmail === 'function') {
                window.deleteEmail(emailId);
                return;
            }

            // 调用统一的判断方法
            this.handleEmailAction(emailId, 'delete', function(url) {
                // 对于删除操作，如果返回的是翻译页面URL，则打开翻译页面
                if (url && url.includes('showTranslation')) {
                    const winWidth = window.innerWidth * 0.9;
                    const winHeight = window.innerHeight * 0.9;

                    layer.open({
                        type: 2,
                        title: '邮件翻译',
                        area: [winWidth + 'px', winHeight + 'px'],
                        maxmin: true,
                        content: url,
                        success: function (layero, index) {
                            $(layero).find('.layui-layer-content').css({
                                'overflow': 'auto',
                                'height': (winHeight - 50) + 'px'
                            });
                        }
                    });
                } else {
                    // 执行原有的删除逻辑
                    layer.confirm('确定要删除这封邮件吗？删除后可在垃圾箱中找回。', {
                        icon: 3,
                        title: '确认删除'
                    }, function (index) {
                        const loadingIndex = layer.msg('正在删除...', {
                            icon: 16,
                            shade: 0.3,
                            time: 0
                        });

                        Ajax.post('/admin/emailMessages/delete', {
                            id: emailId
                        }, function (res) {
                            layer.close(loadingIndex);

                            if (res.state === 'ok') {
                                layer.close(index);
                                LayerMsgBox.success('邮件删除成功');
                                // Ajax方式删除邮件，不刷新页面
                                EmailListTemplate.removeEmailFromList(emailId);
                            } else {
                                LayerMsgBox.alert(res.msg || '删除失败');
                            }
                        }, function (error) {
                            layer.close(loadingIndex);
                            LayerMsgBox.alert('删除请求失败，请稍后重试');
                        });
                    });
                }
            });
        },

        /**
         * 统一处理邮件操作，根据用户角色和邮件类型决定跳转逻辑
         */
        handleEmailAction: function(emailId, action, callback) {
            console.log('[EmailListTemplate] 统一处理邮件操作:', emailId, action);

            // 调用后端判断方法
            Ajax.post('/admin/emailMessages/getEmailActionUrl', {
                emailId: emailId,
                action: action
            }, function(res) {
                if (res.state === 'ok' && res.data && res.data.url) {
                    console.log('[EmailListTemplate] 后端返回URL:', res.data.url);
                    callback(res.data.url);
                } else {
                    console.error('[EmailListTemplate] 后端判断失败:', res.msg);
                    // 如果后端判断失败，执行原有逻辑
                    let defaultUrl = '';
                    switch(action) {
                        case 'reply':
                            defaultUrl = '/admin/emailMessages/replyEmail/' + emailId + '?_jb_rqtype_=dialog';
                            break;
                        case 'translate':
                            defaultUrl = 'admin/emailMessages/showTranslation?emailId=' + emailId + '&_jb_rqtype_=dialog';
                            break;
                        case 'delete':
                            defaultUrl = ''; // 删除操作不需要URL
                            break;
                    }
                    callback(defaultUrl);
                }
            }, function(error) {
                console.error('[EmailListTemplate] 请求后端判断失败:', error);
                // 如果请求失败，执行原有逻辑
                let defaultUrl = '';
                switch(action) {
                    case 'reply':
                        defaultUrl = '/admin/emailMessages/replyEmail/' + emailId + '?_jb_rqtype_=dialog';
                        break;
                    case 'translate':
                        defaultUrl = 'admin/emailMessages/showTranslation?emailId=' + emailId + '&_jb_rqtype_=dialog';
                        break;
                    case 'delete':
                        defaultUrl = '';
                        break;
                }
                callback(defaultUrl);
            });
        },

        // 批量操作方法
        toggleSelectAll: function (pageId) {
            console.log('[EmailListTemplate] 切换全选:', pageId);

            const selectAllCheckbox = document.getElementById(`selectAllEmails_${pageId}`);
            const table = document.getElementById(`email_table_${pageId}`);

            if (!selectAllCheckbox || !table) {
                console.error('[EmailListTemplate] 找不到全选复选框或表格:', pageId);
                return;
            }

            // 获取所有邮件行的复选框
            const emailCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');

            // 根据全选复选框的状态设置所有邮件复选框
            emailCheckboxes.forEach(checkbox => {
                if (checkbox && checkbox !== selectAllCheckbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                }
            });

            this.updateBatchButtons(pageId);
        },
        updateBatchButtons: function (pageId) {
            const table = document.getElementById(`email_table_${pageId}`);
            if (!table) return;

            // 获取选中的复选框
            const selectedCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
            const allCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
            const selectAllCheckbox = document.getElementById(`selectAllEmails_${pageId}`);

            const batchBar = document.getElementById(`batchOperationsBar_${pageId}`);
            const mobileBatchBar = document.getElementById(`mobileBatchOperationsBar_${pageId}`);
            const selectedCount = document.getElementById(`selectedCount_${pageId}`);
            const mobileSelectedCount = document.getElementById(`mobileSelectedCount_${pageId}`);

            // 更新批量操作栏显示状态
            if (selectedCheckboxes.length > 0) {
                if (batchBar) batchBar.style.display = 'block';
                if (mobileBatchBar) mobileBatchBar.style.display = 'block';
                if (selectedCount) selectedCount.textContent = selectedCheckboxes.length;
                if (mobileSelectedCount) mobileSelectedCount.textContent = selectedCheckboxes.length;
            } else {
                if (batchBar) batchBar.style.display = 'none';
                if (mobileBatchBar) mobileBatchBar.style.display = 'none';
            }

            // 更新全选复选框状态
            if (selectAllCheckbox) {
                if (selectedCheckboxes.length === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (selectedCheckboxes.length === allCheckboxes.length) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                    selectAllCheckbox.checked = false;
                }
            }
        },
        clearSelection: function (pageId) {
            const table = document.getElementById(`email_table_${pageId}`);
            const selectAllCheckbox = document.getElementById(`selectAllEmails_${pageId}`);

            if (table) {
                // 取消选中所有邮件复选框
                const emailCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]');
                emailCheckboxes.forEach(checkbox => {
                    if (checkbox) {
                        checkbox.checked = false;
                    }
                });
            }

            // 重置全选复选框
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }

            this.updateBatchButtons(pageId);
        },
        getSelectedIds: function (pageId) {
            const table = document.getElementById(`email_table_${pageId}`);
            if (!table) return [];

            const selectedCheckboxes = table.querySelectorAll('tbody input[type="checkbox"]:checked');
            return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
        },
        batchMarkRead: function (pageId) {
            const selectedIds = this.getSelectedIds(pageId);
            if (selectedIds.length === 0) {
                LayerMsgBox.alert('请先选择要操作的邮件');
                return;
            }

            console.log(`[EmailListTemplate] 批量标记已读 - 选中 ${selectedIds.length} 封邮件:`, selectedIds);

            // 显示操作中提示
            const loadingIndex = layer.msg('正在标记已读...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            // 调用后端API执行批量标记已读
            Ajax.post('/admin/emailMessages/batchMarkRead', {
                ids: selectedIds.join(',')
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    LayerMsgBox.success(`成功标记 ${selectedIds.length} 封邮件为已读`);

                    // Ajax方式批量更新邮件状态
                    selectedIds.forEach(emailId => {
                        EmailListTemplate.updateEmailStatusInList(emailId, 'read', { is_read: true });
                    });

                    // 清除选择
                    EmailListTemplate.clearSelection(pageId);
                } else {
                    LayerMsgBox.alert(res.msg || '批量标记已读失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('批量标记已读失败：网络错误');
                console.error('[EmailListTemplate] 批量标记已读失败:', error);
            });
        },
        batchMarkUnread: function (pageId) {
            const selectedIds = this.getSelectedIds(pageId);
            if (selectedIds.length === 0) {
                LayerMsgBox.alert('请先选择要操作的邮件');
                return;
            }

            console.log(`[EmailListTemplate] 批量标记未读 - 选中 ${selectedIds.length} 封邮件:`, selectedIds);

            // 显示操作中提示
            const loadingIndex = layer.msg('正在标记未读...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            // 调用后端API执行批量标记未读
            Ajax.post('/admin/emailMessages/batchMarkUnread', {
                ids: selectedIds.join(',')
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    LayerMsgBox.success(`成功标记 ${selectedIds.length} 封邮件为未读`);

                    // Ajax方式批量更新邮件状态
                    selectedIds.forEach(emailId => {
                        EmailListTemplate.updateEmailStatusInList(emailId, 'read', { is_read: false });
                    });

                    // 清除选择
                    EmailListTemplate.clearSelection(pageId);
                } else {
                    LayerMsgBox.alert(res.msg || '批量标记未读失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('批量标记未读失败：网络错误');
                console.error('[EmailListTemplate] 批量标记未读失败:', error);
            });
        },
        batchFollowUp: function (pageId) {
            const selectedIds = this.getSelectedIds(pageId);
            if (selectedIds.length === 0) {
                LayerMsgBox.alert('请先选择要操作的邮件');
                return;
            }

            console.log(`[EmailListTemplate] 批量处理 - 选中 ${selectedIds.length} 封邮件:`, selectedIds);

            // 显示操作中提示
            const loadingIndex = layer.msg('正在批量处理...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            // 调用后端API执行批量处理
            Ajax.post('/admin/emailMessages/batchFollowUp', {
                ids: selectedIds.join(',')
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    LayerMsgBox.success(`成功处理 ${selectedIds.length} 封邮件`);

                    // Ajax方式批量更新邮件状态
                    selectedIds.forEach(emailId => {
                        EmailListTemplate.updateEmailStatusInList(emailId, 'followUp', { is_follow_up: true, follow_type: 1 });
                    });

                    // 清除选择
                    EmailListTemplate.clearSelection(pageId);
                } else {
                    LayerMsgBox.alert(res.msg || '批量处理失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('批量处理失败：网络错误');
                console.error('[EmailListTemplate] 批量处理失败:', error);
            });
        },
        batchMarkImportant: function (pageId) {
            const selectedIds = this.getSelectedIds(pageId);
            if (selectedIds.length === 0) {
                LayerMsgBox.alert('请先选择要操作的邮件');
                return;
            }

            console.log(`[EmailListTemplate] 批量标记重要 - 选中 ${selectedIds.length} 封邮件:`, selectedIds);

            // 显示操作中提示
            const loadingIndex = layer.msg('正在标记重要...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            // 调用后端API执行批量标记重要
            Ajax.post('/admin/emailMessages/batchMarkImportant', {
                ids: selectedIds.join(',')
            }, function (res) {
                layer.close(loadingIndex);

                if (res.state === 'ok') {
                    LayerMsgBox.success(`成功标记 ${selectedIds.length} 封邮件为重要`);

                    // Ajax方式批量更新邮件状态
                    selectedIds.forEach(emailId => {
                        EmailListTemplate.updateEmailStatusInList(emailId, 'important', { is_important: true });
                    });

                    // 清除选择
                    EmailListTemplate.clearSelection(pageId);
                } else {
                    LayerMsgBox.alert(res.msg || '批量标记重要失败');
                }
            }, function (error) {
                layer.close(loadingIndex);
                LayerMsgBox.alert('批量标记重要失败：网络错误');
                console.error('[EmailListTemplate] 批量标记重要失败:', error);
            });
        },
        batchDelete: function (pageId) {
            const selectedIds = this.getSelectedIds(pageId);
            if (selectedIds.length === 0) {
                LayerMsgBox.alert('请先选择要操作的邮件');
                return;
            }

            layer.confirm(`确定要删除选中的 ${selectedIds.length} 封邮件吗？删除后可在垃圾箱中找回。`, {
                icon: 3,
                title: '确认删除'
            }, function (index) {
                console.log(`[EmailListTemplate] 批量删除 - 选中 ${selectedIds.length} 封邮件:`, selectedIds);

                // 显示操作中提示
                const loadingIndex = layer.msg('正在删除邮件...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                // 调用后端API执行批量删除
                Ajax.post('/admin/emailMessages/batchDelete', {
                    ids: selectedIds.join(',')
                }, function (res) {
                    layer.close(loadingIndex);

                    if (res.state === 'ok') {
                        layer.close(index);
                        LayerMsgBox.success(`成功删除 ${selectedIds.length} 封邮件`);

                        // Ajax方式批量删除邮件
                        selectedIds.forEach(emailId => {
                            EmailListTemplate.removeEmailFromList(emailId);
                        });

                        // 清除选择
                        EmailListTemplate.clearSelection(pageId);
                    } else {
                        LayerMsgBox.alert(res.msg || '批量删除失败');
                    }
                }, function (error) {
                    layer.close(loadingIndex);
                    LayerMsgBox.alert('批量删除失败：网络错误');
                    console.error('[EmailListTemplate] 批量删除失败:', error);
                });
            });
        },

        // 邮件地址相关操作方法
        showEmailHistory: function (email, name, type) {
            if (typeof JBoltEmailAddressUtil !== 'undefined' && JBoltEmailAddressUtil.showEmailHistory) {
                JBoltEmailAddressUtil.showEmailHistory(email, name, type);
            } else {
                // 简化版本的邮件历史查看
                const title = `${name || email} 的往来邮件`;
                LayerMsgBox.alert(`功能演示：查看 ${title}\n邮件地址：${email}\n类型：${type === 'from' ? '发件人' : '收件人'}`);
            }
        },

        showAllAddresses: function (addressString, nameString, type) {
            if (typeof JBoltEmailAddressUtil !== 'undefined' && JBoltEmailAddressUtil.expandAddresses) {
                JBoltEmailAddressUtil.expandAddresses(addressString, nameString, type);
            } else {
                // 简化版本的地址列表显示
                const addresses = addressString.split(/[,;]/).map(s => s.trim()).filter(s => s.length > 0);
                const names = nameString ? nameString.split(/[,;]/).map(s => s.trim()) : [];

                let content = `<div class="email-addresses-list">`;
                addresses.forEach((addr, index) => {
                    const name = names[index] || extractNameFromAddress(addr);
                    content += `
                        <div class="p-2 border-bottom">
                            <strong>${escapeHtml(name || addr)}</strong><br>
                            <small class="text-muted">${escapeHtml(addr)}</small>
                        </div>
                    `;
                });
                content += `</div>`;

                layer.open({
                    type: 1,
                    title: `${type === 'from' ? '发件人' : '收件人'}列表 (共 ${addresses.length} 个地址)`,
                    area: ['400px', '300px'],
                    content: content,
                    btn: ['关闭'],
                    yes: function (index) {
                        layer.close(index);
                    }
                });
            }
        },

        showContactInfo: function (email, name, type) {
            if (typeof JBoltEmailAddressUtil !== 'undefined' && JBoltEmailAddressUtil.showContactInfo) {
                JBoltEmailAddressUtil.showContactInfo(email, name, type);
            } else {
                // 简化版本的联系人信息
                LayerMsgBox.alert(`联系人信息：\n姓名：${name || '未知'}\n邮箱：${email}\n类型：${type === 'from' ? '发件人' : '收件人'}`);
            }
        },

        // 顶部分页控件隐藏/展示功能
        toggleTopPagination: function (pageId) {
            const topContainer = document.getElementById(`emailPaginationTopContainer_${pageId}`);
            const toggleContainer = document.getElementById(`emailPaginationToggle_${pageId}`);
            const toggleIcon = document.getElementById(`toggleTopPaginationIcon_${pageId}`);
            const toggleBtn = document.getElementById(`toggleTopPagination_${pageId}`);

            if (!topContainer || !toggleContainer) return;

            const isVisible = topContainer.style.display !== 'none';

            if (isVisible) {
                // 隐藏顶部分页控件
                topContainer.classList.add('hiding');
                setTimeout(() => {
                    topContainer.style.display = 'none';
                    topContainer.classList.remove('hiding');
                    toggleContainer.style.display = 'block';
                }, 300);

                // 保存隐藏状态
                localStorage.setItem(`showTopPagination_${pageId}`, 'false');
            } else {
                // 显示顶部分页控件
                toggleContainer.style.display = 'none';
                topContainer.style.display = 'block';

                // 更新按钮状态
                if (toggleIcon) {
                    toggleIcon.className = 'fa fa-eye-slash';
                }
                if (toggleBtn) {
                    toggleBtn.setAttribute('title', '隐藏顶部分页控件');
                }

                // 保存显示状态
                localStorage.setItem(`showTopPagination_${pageId}`, 'true');
            }
        },

        // Ajax更新邮件状态的辅助方法
        updateEmailStatusInList: function (emailId, statusType, newData) {
            console.log('[EmailListTemplate] 更新邮件状态:', emailId, statusType, newData);

            // 遍历所有实例，找到包含该邮件的实例并更新
            for (const pageId in instances) {
                const instance = instances[pageId];
                if (!instance) continue;

                // 在所有邮件数组中查找并更新
                const updateEmailInArray = (emailArray) => {
                    const emailIndex = emailArray.findIndex(email => email.id == emailId);
                    if (emailIndex !== -1) {
                        const email = emailArray[emailIndex];

                        // 根据状态类型更新对应字段
                        switch (statusType) {
                            case 'read':
                                email.is_read = newData && newData.is_read !== undefined ? newData.is_read : !email.is_read;
                                break;
                            case 'followUp':
                                if (newData) {
                                    email.is_follow_up = newData.is_follow_up;
                                    email.follow_type = newData.follow_type;
                                    email.follow_up_user_name = newData.follow_up_user_name;
                                    email.follow_up_time = newData.follow_up_time;
                                } else {
                                    email.is_follow_up = !email.is_follow_up;
                                }
                                break;
                            case 'important':
                                email.is_important = newData && newData.is_important !== undefined ? newData.is_important : !email.is_important;
                                break;
                        }

                        console.log('[EmailListTemplate] 邮件数据已更新:', email);
                        return true;
                    }
                    return false;
                };

                // 更新各个数组中的邮件数据
                let updated = false;
                if (instance.allEmails) {
                    updated = updateEmailInArray(instance.allEmails) || updated;
                }
                if (instance.sortedEmails) {
                    updated = updateEmailInArray(instance.sortedEmails) || updated;
                }

                // 如果找到并更新了邮件，重新渲染该邮件行
                if (updated) {
                    this.updateEmailRowDisplay(pageId, emailId);
                }
            }
        },

        // 更新邮件行显示
        updateEmailRowDisplay: function (pageId, emailId) {
            console.log('[EmailListTemplate] 更新邮件行显示:', pageId, emailId);

            const instance = instances[pageId];
            if (!instance) return;

            // 找到更新后的邮件数据
            let updatedEmail = null;
            if (instance.allEmails) {
                updatedEmail = instance.allEmails.find(email => email.id == emailId);
            }

            if (!updatedEmail) {
                console.warn('[EmailListTemplate] 未找到邮件数据:', emailId);
                return;
            }

            // 更新桌面版表格行
            const desktopRow = document.querySelector(`#email_table_${pageId} tr[data-email-id="${emailId}"]`);
            if (desktopRow) {
                const newRowHtml = renderEmailRow(pageId, updatedEmail);
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newRowHtml;
                const newRow = tempDiv.firstElementChild;

                if (newRow) {
                    desktopRow.parentNode.replaceChild(newRow, desktopRow);
                    console.log('[EmailListTemplate] 桌面版邮件行已更新');
                }
            }

            // 更新移动版卡片
            const mobileCard = document.querySelector(`#mobile_email_list_${pageId} .mobile-email-card[data-email-id="${emailId}"]`);
            if (mobileCard) {
                const newCardHtml = renderMobileCard(pageId, updatedEmail);
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newCardHtml;
                const newCard = tempDiv.firstElementChild;

                if (newCard) {
                    mobileCard.parentNode.replaceChild(newCard, mobileCard);
                    console.log('[EmailListTemplate] 移动版邮件卡片已更新');
                }
            }

            // 重新初始化工具提示
            $('[data-toggle="tooltip"]').tooltip();
        },

        // 从列表中移除邮件
        removeEmailFromList: function (emailId) {
            console.log('[EmailListTemplate] 从列表中移除邮件:', emailId);

            // 遍历所有实例，找到包含该邮件的实例并移除
            for (const pageId in instances) {
                const instance = instances[pageId];
                if (!instance) continue;

                // 从各个数组中移除邮件
                let removed = false;
                if (instance.allEmails) {
                    const index = instance.allEmails.findIndex(email => email.id == emailId);
                    if (index !== -1) {
                        instance.allEmails.splice(index, 1);
                        removed = true;
                    }
                }
                if (instance.sortedEmails) {
                    const index = instance.sortedEmails.findIndex(email => email.id == emailId);
                    if (index !== -1) {
                        instance.sortedEmails.splice(index, 1);
                        removed = true;
                    }
                }

                // 如果移除了邮件，更新显示
                if (removed) {
                    // 移除DOM元素
                    const desktopRow = document.querySelector(`#email_table_${pageId} tr[data-email-id="${emailId}"]`);
                    if (desktopRow) {
                        desktopRow.remove();
                        console.log('[EmailListTemplate] 桌面版邮件行已移除');
                    }

                    const mobileCard = document.querySelector(`#mobile_email_list_${pageId} .mobile-email-card[data-email-id="${emailId}"]`);
                    if (mobileCard) {
                        mobileCard.remove();
                        console.log('[EmailListTemplate] 移动版邮件卡片已移除');
                    }

                    // 更新分页信息
                    if (instance.config.showPagination) {
                        if (instance.totalCount !== undefined) {
                            // 服务端分页：减少总数
                            instance.totalCount = Math.max(0, instance.totalCount - 1);
                            updateServerPaginationInfo(pageId);
                        } else {
                            // 客户端分页：重新计算分页
                            instance.pagination.totalPages = Math.ceil(instance.sortedEmails.length / instance.pagination.pageSize);
                            updatePaginationInfo(pageId);
                        }
                    }

                    // 如果当前页没有邮件了，显示空状态
                    const tbody = document.getElementById(`email_tbody_${pageId}`);
                    if (tbody && tbody.children.length === 0) {
                        const colCount = instance.config.showBatchActions ? 8 : 7;
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="${colCount}" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fa fa-inbox fa-3x mb-3"></i>
                                        <p>暂无邮件</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }

                    const mobileContainer = document.getElementById(`mobile_email_list_${pageId}`);
                    if (mobileContainer && mobileContainer.children.length === 0) {
                        mobileContainer.innerHTML = `
                            <div class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fa fa-inbox fa-3x mb-3"></i>
                                    <p>暂无邮件</p>
                                </div>
                            </div>
                        `;
                    }
                }
            }
        },

        // 跟踪相关方法
        loadTrackingInfo: loadTrackingInfo,
        showTrackingDetail: showTrackingDetail
    };

    // 内部函数
    function updateSortIcons(pageId) {
        const table = document.getElementById(`email_table_${pageId}`);
        const instance = instances[pageId];
        if (!table || !instance) return;

        // 清除所有排序样式
        const sortableCols = table.querySelectorAll('.sortable-col');
        sortableCols.forEach(col => {
            col.classList.remove('sort-asc', 'sort-desc');
            const icon = col.querySelector('.sort-icon');
            if (icon) {
                icon.className = 'fa fa-sort sort-icon text-muted';
            }
        });

        // 设置当前排序列的样式
        const currentSortCol = table.querySelector(`[data-sort="${instance.sortState.field}"]`);
        if (currentSortCol) {
            currentSortCol.classList.add(`sort-${instance.sortState.direction}`);
            const icon = currentSortCol.querySelector('.sort-icon');
            if (icon) {
                icon.className = `fa fa-sort-${instance.sortState.direction === 'asc' ? 'up' : 'down'} sort-icon`;
                icon.style.color = '#007bff';
            }
        }
    }

    function updatePaginationInfo(pageId) {
        console.log('[分页调试] updatePaginationInfo 开始，pageId:', pageId);

        const instance = instances[pageId];
        if (!instance) {
            console.log('[分页调试] 实例不存在:', pageId);
            return;
        }

        if (!instance.config.showPagination) {
            console.log('[分页调试] 配置中禁用分页显示:', instance.config);
            return;
        }

        const {sortedEmails, pagination} = instance;
        console.log('[分页调试] 邮件数据:', {
            emailCount: sortedEmails.length,
            pageSize: pagination.pageSize,
            currentPage: pagination.currentPage,
            totalPages: pagination.totalPages
        });

        // 顶部分页控件
        const paginationTopContainer = document.getElementById(`emailPaginationTopContainer_${pageId}`);
        const paginationTopInfo = document.getElementById(`paginationTopInfo_${pageId}`);
        const paginationTopList = document.getElementById(`emailPaginationTop_${pageId}`);

        // 底部分页控件
        const paginationBottomContainer = document.getElementById(`emailPaginationBottomContainer_${pageId}`);
        const paginationBottomInfo = document.getElementById(`paginationBottomInfo_${pageId}`);
        const paginationBottomList = document.getElementById(`emailPaginationBottom_${pageId}`);

        console.log('[分页调试] DOM元素查找结果:', {
            pageId,
            topContainer: !!paginationTopContainer,
            topInfo: !!paginationTopInfo,
            topList: !!paginationTopList,
            bottomContainer: !!paginationBottomContainer,
            bottomInfo: !!paginationBottomInfo,
            bottomList: !!paginationBottomList
        });

        // 检查是否需要显示分页控件
        // 对于客户端分页：检查总邮件数量
        // 对于服务端分页：检查totalCount（如果存在）
        const totalCount = instance.totalCount;
        const shouldShowPagination = totalCount !== undefined ?
            (totalCount > pagination.pageSize) :
            (sortedEmails.length > pagination.pageSize);

        console.log('[分页调试] 分页显示判断:', {
            totalCount,
            sortedEmailsLength: sortedEmails.length,
            pageSize: pagination.pageSize,
            shouldShowPagination
        });

        if (!shouldShowPagination) {
            console.log('[分页调试] 邮件数量不足，隐藏分页控件');
            if (paginationTopContainer) paginationTopContainer.style.display = 'none';
            if (paginationBottomContainer) paginationBottomContainer.style.display = 'none';
            return;
        }

        // 显示分页控件（根据用户设置决定是否显示顶部分页）
        const showTopPagination = localStorage.getItem(`showTopPagination_${pageId}`) !== 'false';
        console.log('[分页调试] 显示设置:', {
            showTopPagination,
            localStorageValue: localStorage.getItem(`showTopPagination_${pageId}`)
        });

        if (paginationTopContainer) {
            paginationTopContainer.style.display = showTopPagination ? 'block' : 'none';
            console.log('[分页调试] 顶部分页控件显示状态设置为:', showTopPagination ? 'block' : 'none');
        }
        if (paginationBottomContainer) {
            paginationBottomContainer.style.display = 'block';
            console.log('[分页调试] 底部分页控件显示状态设置为: block');
        }

        // 更新分页信息文本
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize + 1;
        const endIndex = Math.min(pagination.currentPage * pagination.pageSize, sortedEmails.length);
        const infoText = `显示第 ${startIndex}-${endIndex} 条，共 ${sortedEmails.length} 条记录`;

        console.log('[分页调试] 分页信息文本:', infoText);

        if (paginationTopInfo) {
            paginationTopInfo.textContent = infoText;
        }
        if (paginationBottomInfo) {
            paginationBottomInfo.textContent = infoText;
        }

        // 生成分页按钮 - 根据分页模式选择正确的函数
        const isServerPagination = instance.totalCount !== undefined;
        console.log('[分页调试] 分页模式:', isServerPagination ? '服务端分页' : '客户端分页');

        if (paginationTopList) {
            if (isServerPagination) {
                generateServerPaginationButtons(pageId, 'Top');
            } else {
                generatePaginationButtons(pageId, 'Top');
            }
            console.log('[分页调试] 顶部分页按钮已生成');
        }
        if (paginationBottomList) {
            if (isServerPagination) {
                generateServerPaginationButtons(pageId, 'Bottom');
            } else {
                generatePaginationButtons(pageId, 'Bottom');
            }
            console.log('[分页调试] 底部分页按钮已生成');
        }

        console.log('[分页调试] updatePaginationInfo 完成');
    }

    function updateServerPaginationInfo(pageId) {
        console.log('[服务端分页调试] updateServerPaginationInfo 开始，pageId:', pageId);

        const instance = instances[pageId];
        if (!instance) {
            console.log('[服务端分页调试] 实例不存在:', pageId);
            return;
        }

        if (!instance.config.showPagination) {
            console.log('[服务端分页调试] 配置中禁用分页显示:', instance.config);
            return;
        }

        const {pagination} = instance;
        const totalCount = instance.totalCount || 0;

        console.log('[服务端分页调试] 分页数据:', {
            totalCount,
            currentPage: pagination.currentPage,
            totalPages: pagination.totalPages,
            pageSize: pagination.pageSize
        });

        // 顶部分页控件
        const paginationTopContainer = document.getElementById(`emailPaginationTopContainer_${pageId}`);
        const paginationTopInfo = document.getElementById(`paginationTopInfo_${pageId}`);
        const paginationTopList = document.getElementById(`emailPaginationTop_${pageId}`);

        // 底部分页控件
        const paginationBottomContainer = document.getElementById(`emailPaginationBottomContainer_${pageId}`);
        const paginationBottomInfo = document.getElementById(`paginationBottomInfo_${pageId}`);
        const paginationBottomList = document.getElementById(`emailPaginationBottom_${pageId}`);

        console.log('[服务端分页调试] DOM元素查找结果:', {
            pageId,
            topContainer: !!paginationTopContainer,
            topInfo: !!paginationTopInfo,
            topList: !!paginationTopList,
            bottomContainer: !!paginationBottomContainer,
            bottomInfo: !!paginationBottomInfo,
            bottomList: !!paginationBottomList
        });

        // 如果总数为0或只有一页，隐藏分页控件
        if (totalCount === 0 || pagination.totalPages <= 1) {
            console.log('[服务端分页调试] 数据不足，隐藏分页控件');
            if (paginationTopContainer) paginationTopContainer.style.display = 'none';
            if (paginationBottomContainer) paginationBottomContainer.style.display = 'none';
            return;
        }

        // 显示分页控件（根据用户设置决定是否显示顶部分页）
        const showTopPagination = localStorage.getItem(`showTopPagination_${pageId}`) !== 'false';
        console.log('[服务端分页调试] 显示设置:', {
            showTopPagination,
            localStorageValue: localStorage.getItem(`showTopPagination_${pageId}`)
        });

        if (paginationTopContainer) {
            paginationTopContainer.style.display = showTopPagination ? 'block' : 'none';
            console.log('[服务端分页调试] 顶部分页控件显示状态设置为:', showTopPagination ? 'block' : 'none');
        }
        if (paginationBottomContainer) {
            paginationBottomContainer.style.display = 'block';
            console.log('[服务端分页调试] 底部分页控件显示状态设置为: block');
        }

        // 更新分页信息文本
        const startIndex = (pagination.currentPage - 1) * pagination.pageSize + 1;
        const endIndex = Math.min(pagination.currentPage * pagination.pageSize, totalCount);
        const infoText = `显示第 ${startIndex}-${endIndex} 条，共 ${totalCount} 条记录`;

        console.log('[服务端分页调试] 分页信息文本:', infoText);

        if (paginationTopInfo) {
            paginationTopInfo.textContent = infoText;
        }
        if (paginationBottomInfo) {
            paginationBottomInfo.textContent = infoText;
        }

        // 生成分页按钮
        if (paginationTopList) {
            generateServerPaginationButtons(pageId, 'Top');
            console.log('[服务端分页调试] 顶部分页按钮已生成');
        }
        if (paginationBottomList) {
            generateServerPaginationButtons(pageId, 'Bottom');
            console.log('[服务端分页调试] 底部分页按钮已生成');
        }

        console.log('[服务端分页调试] updateServerPaginationInfo 完成');
    }

    function generatePaginationButtons(pageId, position = '') {
        const instance = instances[pageId];
        const paginationList = document.getElementById(`emailPagination${position}_${pageId}`);
        if (!instance || !paginationList) return;

        const {currentPage, totalPages} = instance.pagination;
        let html = '';

        // 上一页按钮
        const prevDisabled = currentPage === 1 ? 'disabled' : '';
        html += `
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="EmailListTemplate.changePage('${pageId}', ${currentPage - 1}); return false;" ${prevDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fa fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="EmailListTemplate.changePage('${pageId}', 1); return false;">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const active = i === currentPage ? 'active' : '';
            html += `
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="EmailListTemplate.changePage('${pageId}', ${i}); return false;">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="EmailListTemplate.changePage('${pageId}', ${totalPages}); return false;">${totalPages}</a></li>`;
        }

        // 下一页按钮
        const nextDisabled = currentPage === totalPages ? 'disabled' : '';
        html += `
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="EmailListTemplate.changePage('${pageId}', ${currentPage + 1}); return false;" ${nextDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fa fa-chevron-right"></i>
                </a>
            </li>
        `;

        paginationList.innerHTML = html;
    }

    function generateServerPaginationButtons(pageId, position = '') {
        const instance = instances[pageId];
        const paginationList = document.getElementById(`emailPagination${position}_${pageId}`);
        if (!instance || !paginationList) return;

        const {currentPage, totalPages} = instance.pagination;
        let html = '';

        // 上一页按钮
        const prevDisabled = currentPage === 1 ? 'disabled' : '';
        html += `
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changeDashboardPage(${currentPage - 1}); return false;" ${prevDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fa fa-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码按钮
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changeDashboardPage(1); return false;">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const active = i === currentPage ? 'active' : '';
            html += `
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="changeDashboardPage(${i}); return false;">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="changeDashboardPage(${totalPages}); return false;">${totalPages}</a></li>`;
        }

        // 下一页按钮
        const nextDisabled = currentPage === totalPages ? 'disabled' : '';
        html += `
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changeDashboardPage(${currentPage + 1}); return false;" ${nextDisabled ? 'tabindex="-1"' : ''}>
                    <i class="fa fa-chevron-right"></i>
                </a>
            </li>
        `;

        paginationList.innerHTML = html;
    }

    /**
     * 列调整功能
     */
    function initColumnResize(pageId) {
        const table = document.getElementById(`email_table_${pageId}`);
        if (!table) return;

        // 简单的列宽调整功能
        const headers = table.querySelectorAll('th');
        headers.forEach(header => {
            header.style.resize = 'horizontal';
            header.style.overflow = 'hidden';
        });
    }

    /**
     * 显示加载状态
     */
    function showLoadingState(pageId) {
        const tbody = document.getElementById(`email_tbody_${pageId}`);
        if (tbody) {
            const colCount = instances[pageId]?.config.showBatchActions ? 7 : 6;
            tbody.innerHTML = `
                <tr>
                    <td colspan="${colCount}" class="text-center py-5">
                        <div class="d-flex flex-column justify-content-center align-items-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p class="text-muted">正在获取邮件数据...</p>
                        </div>
                    </td>
                </tr>
            `;
        }

        // 移动端加载状态
        const mobileContainer = document.getElementById(`mobile_email_list_${pageId}`);
        if (mobileContainer) {
            mobileContainer.innerHTML = `
                <div class="text-center py-4">
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="spinner-border spinner-border-sm text-primary mr-2" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <span>正在获取邮件，请稍候...</span>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示错误状态
     */
    function showErrorState(pageId, message) {
        const tbody = document.getElementById(`email_tbody_${pageId}`);
        if (tbody) {
            const colCount = instances[pageId]?.config.showBatchActions ? 9 : 8;
            tbody.innerHTML = `
                <tr>
                    <td colspan="${colCount}" class="text-center py-5">
                        <div class="d-flex flex-column justify-content-center align-items-center">
                            <div class="text-danger mb-3">
                                <i class="fa fa-exclamation-circle fa-3x"></i>
                            </div>
                            <h5 class="text-danger mb-3">加载失败</h5>
                            <p class="text-muted mb-3">${message}</p>
                            <button class="btn btn-primary" onclick="EmailListTemplate.loadData('${pageId}')">
                                <i class="fa fa-refresh mr-1"></i> 重新加载
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        // 移动端错误状态
        const mobileContainer = document.getElementById(`mobile_email_list_${pageId}`);
        if (mobileContainer) {
            mobileContainer.innerHTML = `
                <div class="text-center py-4">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <div class="text-danger mb-3">
                            <i class="fa fa-exclamation-circle fa-2x"></i>
                        </div>
                        <p class="text-danger mb-3">${message}</p>
                        <button class="btn btn-primary btn-sm" onclick="EmailListTemplate.loadData('${pageId}')">
                            <i class="fa fa-refresh mr-1"></i> 重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    };

    console.log('[调试] EmailListTemplate IIFE 执行完成，返回对象');
})();

console.log('[调试] email-list-template.js 加载完成，EmailListTemplate:', typeof window.EmailListTemplate);