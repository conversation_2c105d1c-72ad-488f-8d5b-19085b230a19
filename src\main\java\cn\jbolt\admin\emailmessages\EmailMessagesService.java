package cn.jbolt.admin.emailmessages;

import static cn.jbolt.common.util.StringKit.getDisplayName;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jfinal.aop.Inject;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.SqlPara;

import cn.jbolt.admin.emailaccount.EmailAccountService;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.llm.service.LlmService;

/**
 * 邮件消息服务类
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
public class EmailMessagesService extends JBoltBaseService<EmailMessages> {

    private EmailMessages dao = new EmailMessages().dao();

    @Inject
    private EmailAccountService emailAccountService;

    @Override
    protected EmailMessages dao() {
        return dao;
    }

    /**
     * 分页查询邮件
     *
     * @param pageNumber 页码
     * @param pageSize   每页数量
     * @param params     查询参数
     * @return 分页结果
     */
    public Page<Record> paginateEmails(int pageNumber, int pageSize, Kv params) {
        if (params.isEmpty()) {
            return new Page<>(new ArrayList<>(), pageNumber, pageSize, 0, 0);
        }

        SqlPara sqlPara = Db.getSqlPara("emailMessages.findByParams", params);
        return Db.paginate(pageNumber, pageSize, sqlPara);
    }

    /**
     * 获取邮件详情
     *
     * @param id 邮件ID
     * @return 邮件详情
     */
    public Map<String, Object> getEmailDetail(Long id) {
        if (id == null) {
            return null;
        }

        Kv params = Kv.by("id", id);
        Record email = Db.findFirst(Db.getSqlPara("emailMessages.findDetailById", params));

        if (email == null) {
            return null;
        }

        // 标记为已读
        markAsRead(id);

        Map<String, Object> result = new HashMap<>();
        result.put("email", email);

        return result;
    }

    /**
     * 标记邮件为已读
     *
     * @param id 邮件ID
     */
    private void markAsRead(Long id) {
        Db.update("UPDATE email_messages SET is_read = 1 WHERE id = ?", id);
    }

    /**
     * 删除邮件
     *
     * @param id 邮件ID
     * @return 是否成功
     */
    public boolean deleteEmail(Long id) {
        if (id == null) {
            return false;
        }

        // 检查邮件是否存在且属于当前用户
        Record email = Db.findFirst("SELECT * FROM email_messages WHERE id = ?", id);
        if (email == null) {
            return false;
        }
        Db.update("update email_messages set is_delete=1 where id = ?", id);
        return true;
    }

    @Override
    protected int systemLogTargetType() {
        return 0; // 根据系统实际情况设置
    }

    /**
     * 获取邮件往来历史
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param params   查询参数
     * @return 查询结果
     */
    public Ret getEmailHistory(int page, int pageSize, Kv params) {
        try {
            String email = params.getStr("email");
            if (StrKit.isBlank(email)) {
                return Ret.fail("邮箱地址不能为空");
            }

            // 获取当前登录用户ID
            Long currentUserId = JBoltUserKit.getUserId();
            if (currentUserId == null) {
                return Ret.fail("用户未登录");
            }

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            LogKit.info("[邮件历史] 开始获取邮件历史, 邮箱: {}, 页码: {}, 每页: {}, 用户ID: {}", email, page, pageSize, currentUserId);

            // 检查查询的邮箱是否是当前用户管理的邮箱账号
            boolean isUserManagedEmail = Db.queryInt("SELECT COUNT(*) FROM user_email WHERE user_id = ? AND email = ?",
                currentUserId, email) > 0;

            LogKit.info("[邮件历史] 邮箱权限检查 - 邮箱: {}, 是否为用户管理: {}", email, isUserManagedEmail);

            // 构建 SQL 查询，与dashboard保持一致的字段和处理方式
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, ");
            sqlBuilder.append("s.from_address, s.to_address, s.cc_address, s.sent_date, s.has_attachments, ");
            sqlBuilder.append("s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, s.follow_type, ");
            sqlBuilder.append("u.name as follow_up_user_name, s.is_important, ");
            sqlBuilder.append("substr(s.content_html, 1, 500) as content_html, ");
            sqlBuilder.append("CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related ");
            sqlBuilder.append("FROM email_messages s ");
            sqlBuilder.append("LEFT JOIN jb_user u ON s.follow_up_id = u.id ");
            sqlBuilder.append("LEFT JOIN vw_user_email v ON s.id = v.emails_id ");

            List<Object> sqlParams = new ArrayList<>();

            if (isUserManagedEmail) {
                // 如果是用户管理的邮箱，可以查看该邮箱的所有来往邮件
                sqlBuilder.append("WHERE (s.from_address = ? OR s.to_address LIKE ? OR s.cc_address LIKE ?) ");
                sqlBuilder.append("AND ifnull(s.is_delete, 0) <> 1 ");
                sqlParams.add(email);
                sqlParams.add("%" + email + "%");
                sqlParams.add("%" + email + "%");
                LogKit.info("[邮件历史] 使用完整权限查询 - 可查看邮箱所有来往邮件");
            } else {
                // 如果不是用户管理的邮箱，只能查看该邮箱与用户管理的邮箱账号之间的来往邮件
                sqlBuilder.append("WHERE ((s.from_address = ? AND s.to_address IN (SELECT email FROM user_email WHERE user_id = ?)) ");
                sqlBuilder.append("OR (s.from_address IN (SELECT email FROM user_email WHERE user_id = ?) AND s.to_address LIKE ?) ");
                sqlBuilder.append("OR (s.from_address = ? AND s.cc_address IN (SELECT email FROM user_email WHERE user_id = ?)) ");
                sqlBuilder.append("OR (s.from_address IN (SELECT email FROM user_email WHERE user_id = ?) AND s.cc_address LIKE ?)) ");
                sqlBuilder.append("AND ifnull(s.is_delete, 0) <> 1 ");
                sqlParams.add(email);  // from_address = ?
                sqlParams.add(currentUserId);  // to_address IN (SELECT email FROM user_email WHERE user_id = ?)
                sqlParams.add(currentUserId);  // from_address IN (SELECT email FROM user_email WHERE user_id = ?)
                sqlParams.add("%" + email + "%");  // to_address LIKE ?
                sqlParams.add(email);  // from_address = ?
                sqlParams.add(currentUserId);  // cc_address IN (SELECT email FROM user_email WHERE user_id = ?)
                sqlParams.add(currentUserId);  // from_address IN (SELECT email FROM user_email WHERE user_id = ?)
                sqlParams.add("%" + email + "%");  // cc_address LIKE ?
                LogKit.info("[邮件历史] 使用限制权限查询 - 只能查看与用户管理邮箱的来往邮件");
            }

            // 处理客户邮件过滤
            Boolean customerOnly = params.getBoolean("customerOnly");
            if (Boolean.TRUE.equals(customerOnly)) {
                sqlBuilder.append("AND v.client_id is not null and v.client_id <> '' ");
                LogKit.info("[邮件历史] 启用客户邮件过滤");
            }

            // 处理其他过滤条件
            if (StrKit.notBlank(params.getStr("typeFilter"))) {
                String typeFilter = params.getStr("typeFilter");
                if ("received".equals(typeFilter)) {
                    sqlBuilder.append("AND s.from_address != ? ");
                    sqlParams.add(email);
                } else if ("sent".equals(typeFilter)) {
                    sqlBuilder.append("AND s.from_address = ? ");
                    sqlParams.add(email);
                }
            }

            if (StrKit.notBlank(params.getStr("readStatusFilter"))) {
                String readStatusFilter = params.getStr("readStatusFilter");
                if ("read".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 1 ");
                } else if ("unread".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 0 ");
                }
            }

            if (StrKit.notBlank(params.getStr("subjectSearch"))) {
                sqlBuilder.append("AND ( s.subject LIKE ? or s.content_html like ? or s.content_text like ? ) ");
                sqlParams.add("%" + params.getStr("subjectSearch") + "%");
                sqlParams.add("%" + params.getStr("subjectSearch") + "%");
                sqlParams.add("%" + params.getStr("subjectSearch") + "%");
            }

            // 添加GROUP BY确保邮件ID唯一
            sqlBuilder.append("GROUP BY s.id ");

            // 构建排序
            String sortField = params.getStr("sortField", "sent_date");
            String sortDirection = params.getStr("sortDirection", "desc");
            if ("subject".equals(sortField)) {
                sqlBuilder.append("ORDER BY s.subject ").append(sortDirection);
            } else {
                sqlBuilder.append("ORDER BY s.sent_date ").append(sortDirection);
            }

            // 查询总数（用于分页）
            String countSql = "SELECT COUNT(*) FROM (SELECT s.id " +
                    sqlBuilder.substring(sqlBuilder.indexOf("FROM"), sqlBuilder.indexOf("ORDER BY")) + ") AS temp";

            long beforeCountQuery = System.currentTimeMillis();
            Integer totalCount = Db.queryInt(countSql, sqlParams.toArray());
            long countQueryTime = System.currentTimeMillis() - beforeCountQuery;
            LogKit.info("[邮件历史] 统计查询耗时: {}ms, 总数: {}", countQueryTime, totalCount);

            // 计算分页信息
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            int offset = (page - 1) * pageSize;

            // 添加分页限制
            sqlBuilder.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 执行主查询
            long beforeQuery = System.currentTimeMillis();
            List<Record> emails = Db.find(sqlBuilder.toString(), sqlParams.toArray());
            long queryTime = System.currentTimeMillis() - beforeQuery;
            LogKit.info("[邮件历史] 数据查询耗时: {}ms, 获取邮件数量: {}", queryTime, emails.size());

            processEmailData(emails, null);

            // 获取统计信息
            Map<String, Object> stats = getEmailHistoryStats(email);

            // 构建返回结果，与dashboard保持一致的结构
            Map<String, Object> result = new HashMap<>();
            result.put("list", emails);
            result.put("total", totalCount);
            result.put("currentPage", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            result.put("stats", stats);

            long totalTime = System.currentTimeMillis() - startTime;
            LogKit.info("[邮件历史] 获取邮件历史完成，总耗时: {}ms", totalTime);

            return Ret.ok("data", result);

        } catch (Exception e) {
            LogKit.error("获取邮件历史失败", e);
            return Ret.fail("获取邮件历史失败：" + e.getMessage());
        }
    }

    /**
     * 从HTML内容中提取纯文本（与dashboard保持一致）
     * 使用正则表达式快速去除HTML标签
     * 
     * @param html HTML内容
     * @return 纯文本内容
     */
    private String extractPlainText(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }

        // 1. 去除script和style标签及其内容
        html = html.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        html = html.replaceAll("(?i)<style[^>]*>.*?</style>", "");

        // 2. 去除所有HTML标签
        html = html.replaceAll("<[^>]+>", "");

        // 3. 解码HTML实体
        html = html.replaceAll("&nbsp;", " ");
        html = html.replaceAll("&amp;", "&");
        html = html.replaceAll("&lt;", "<");
        html = html.replaceAll("&gt;", ">");
        html = html.replaceAll("&quot;", "\"");
        html = html.replaceAll("&#39;", "'");

        // 4. 去除多余的空白字符
        html = html.replaceAll("\\s+", " ");

        return html.trim();
    }

    /**
     * 获取邮件历史统计信息
     */
    private Map<String, Object> getEmailHistoryStats(String email) {
        Map<String, Object> stats = new HashMap<>();

        // 总邮件数
        Long total = Db.queryLong(
                "SELECT COUNT(*) FROM email_messages WHERE (from_address = ? OR to_address LIKE ? OR cc_address LIKE ?) AND is_delete = 0",
                email, "%" + email + "%", "%" + email + "%");

        // 收到的邮件数
        Long received = Db.queryLong("SELECT COUNT(*) FROM email_messages WHERE to_address LIKE ? AND is_delete = 0",
                "%" + email + "%");

        // 发出的邮件数
        Long sent = Db.queryLong("SELECT COUNT(*) FROM email_messages WHERE from_address = ? AND is_delete = 0",
                email);

        // 未读邮件数
        Long unread = Db.queryLong(
                "SELECT COUNT(*) FROM email_messages WHERE (from_address = ? OR to_address LIKE ? OR cc_address LIKE ?) AND is_read = 0 AND is_delete = 0",
                email, "%" + email + "%", "%" + email + "%");

        stats.put("total", total != null ? total : 0);
        stats.put("received", received != null ? received : 0);
        stats.put("sent", sent != null ? sent : 0);
        stats.put("unread", unread != null ? unread : 0);

        return stats;
    }

    /**
     * 获取用户的邮件过滤预设列表
     *
     * @param userId 用户ID
     * @return 预设列表
     */
    public List<Record> getFilterPresets(Long userId) {
        System.out.println("[EmailMessagesService] 获取用户预设，用户ID: " + userId);
        List<Record> presets = Db.find(
                "SELECT * FROM email_filter_preset WHERE user_id = ? ORDER BY is_default DESC, created_at DESC",
                userId);
        System.out.println("[EmailMessagesService] 查询到的预设数量: " + presets.size());
        for (Record preset : presets) {
            System.out.println("[EmailMessagesService] 预设详情: id=" + preset.getLong("id") +
                    ", name=" + preset.getStr("name") +
                    ", is_default=" + preset.getBoolean("is_default") +
                    ", filters=" + preset.getStr("filters") +
                    ", logic=" + preset.getStr("logic"));
        }
        return presets;
    }

    /**
     * 保存邮件过滤预设
     *
     * @param userId  用户ID
     * @param name    预设名称
     * @param filters 过滤条件JSON
     * @param logic   逻辑运算符
     * @return 操作结果
     */
    public Ret saveFilterPreset(Long userId, String name, String filters, String logic) {
        try {
            // 检查是否已存在同名预设
            Record existing = Db.findFirst("SELECT id FROM email_filter_preset WHERE user_id = ? AND name = ?", userId,
                    name);
            if (existing != null) {
                return Ret.fail("msg", "已存在同名预设，请使用其他名称");
            }

            Record preset = new Record();
            preset.set("user_id", userId);
            preset.set("name", name);
            preset.set("filters", filters);
            preset.set("logic", logic);
            preset.set("created_at", new java.util.Date());
            preset.set("updated_at", new java.util.Date());

            boolean success = Db.save("email_filter_preset", preset);
            if (success) {
                return Ret.ok("msg", "预设保存成功").set("data", preset);
            } else {
                return Ret.fail("msg", "预设保存失败");
            }
        } catch (Exception e) {
            return Ret.fail("msg", "预设保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除邮件过滤预设
     *
     * @param userId   用户ID
     * @param presetId 预设ID
     * @return 操作结果
     */
    public Ret deleteFilterPreset(Long userId, Long presetId) {
        try {
            // 验证预设是否属于当前用户
            Record preset = Db.findFirst("SELECT * FROM email_filter_preset WHERE id = ? AND user_id = ?", presetId,
                    userId);
            if (preset == null) {
                return Ret.fail("msg", "预设不存在或无权删除");
            }

            boolean success = Db.deleteById("email_filter_preset", presetId);
            if (success) {
                return Ret.ok("msg", "预设删除成功");
            } else {
                return Ret.fail("msg", "预设删除失败");
            }
        } catch (Exception e) {
            return Ret.fail("msg", "预设删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定的邮件过滤预设
     *
     * @param userId   用户ID
     * @param presetId 预设ID
     * @return 预设信息
     */
    public Record getFilterPreset(Long userId, Long presetId) {
        return Db.findFirst("SELECT * FROM email_filter_preset WHERE id = ? AND user_id = ?", presetId, userId);
    }

    /**
     * 切换预设的默认状态
     *
     * @param userId   用户ID
     * @param presetId 预设ID
     * @return 操作结果
     */
    public Ret toggleDefaultPreset(Long userId, Long presetId) {
        try {
            // 验证预设是否属于当前用户
            Record preset = Db.findFirst("SELECT * FROM email_filter_preset WHERE id = ? AND user_id = ?", presetId,
                    userId);
            if (preset == null) {
                return Ret.fail("msg", "预设不存在或无权操作");
            }

            Boolean currentDefault = preset.getBoolean("is_default");
            boolean newDefault = !Boolean.TRUE.equals(currentDefault);

            if (newDefault) {
                // 如果要设置为默认，先取消该用户的其他默认预设
                Db.update("UPDATE email_filter_preset SET is_default = 0 WHERE user_id = ? AND id != ?", userId,
                        presetId);
            }

            // 更新当前预设的默认状态
            boolean success = Db.update("UPDATE email_filter_preset SET is_default = ?, updated_at = ? WHERE id = ?",
                    newDefault ? 1 : 0, new java.util.Date(), presetId) > 0;

            if (success) {
                // 重新查询更新后的预设信息
                Record updatedPreset = Db.findFirst("SELECT * FROM email_filter_preset WHERE id = ?", presetId);
                return Ret.ok("msg", newDefault ? "已设为默认预设" : "已取消默认预设")
                        .set("data", updatedPreset);
            } else {
                return Ret.fail("msg", "设置默认预设失败");
            }
        } catch (Exception e) {
            return Ret.fail("msg", "设置默认预设失败: " + e.getMessage());
        }
    }

    /**
     * 保存邮件过滤预设（重载方法，支持默认预设）
     *
     * @param userId    用户ID
     * @param name      预设名称
     * @param filters   过滤条件JSON
     * @param logic     逻辑运算符
     * @param isDefault 是否设为默认
     * @return 操作结果
     */
    public Ret saveFilterPreset(Long userId, String name, String filters, String logic, Boolean isDefault) {
        try {
            // 检查是否已存在同名预设
            Record existing = Db.findFirst("SELECT id FROM email_filter_preset WHERE user_id = ? AND name = ?", userId,
                    name);
            if (existing != null) {
                return Ret.fail("msg", "已存在同名预设，请使用其他名称");
            }

            // 如果要设置为默认，先取消该用户的其他默认预设
            if (Boolean.TRUE.equals(isDefault)) {
                Db.update("UPDATE email_filter_preset SET is_default = 0 WHERE user_id = ?", userId);
            }

            Record preset = new Record();
            preset.set("user_id", userId);
            preset.set("name", name);
            preset.set("filters", filters);
            preset.set("logic", logic);
            preset.set("is_default", Boolean.TRUE.equals(isDefault) ? 1 : 0);
            preset.set("created_at", new java.util.Date());
            preset.set("updated_at", new java.util.Date());

            boolean success = Db.save("email_filter_preset", preset);
            if (success) {
                return Ret.ok("msg", "预设保存成功").set("data", preset);
            } else {
                return Ret.fail("msg", "预设保存失败");
            }
        } catch (Exception e) {
            return Ret.fail("msg", "预设保存失败: " + e.getMessage());
        }
    }

    /**
     * 批量标记邮件已读
     *
     * @param ids 邮件ID列表
     * @return 操作结果
     */
    public Ret batchMarkRead(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Ret.fail("msg", "邮件ID列表不能为空");
        }

        try {
            StringBuilder sql = new StringBuilder(
                    "UPDATE email_messages SET is_read = 1, updated_at = ? WHERE id IN (");
            List<Object> params = new ArrayList<>();
            params.add(new Date());

            for (int i = 0; i < ids.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
                params.add(Long.valueOf(ids.get(i)));
            }
            sql.append(")");

            int count = Db.update(sql.toString(), params.toArray());
            return Ret.ok("msg", "成功标记 " + count + " 封邮件为已读");
        } catch (Exception e) {
            return Ret.fail("msg", "批量标记已读失败：" + e.getMessage());
        }
    }

    /**
     * 批量标记邮件未读
     *
     * @param ids 邮件ID列表
     * @return 操作结果
     */
    public Ret batchMarkUnread(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Ret.fail("msg", "邮件ID列表不能为空");
        }

        try {
            StringBuilder sql = new StringBuilder(
                    "UPDATE email_messages SET is_read = 0, updated_at = ? WHERE id IN (");
            List<Object> params = new ArrayList<>();
            params.add(new Date());

            for (int i = 0; i < ids.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
                params.add(Long.valueOf(ids.get(i)));
            }
            sql.append(")");

            int count = Db.update(sql.toString(), params.toArray());
            return Ret.ok("msg", "成功标记 " + count + " 封邮件为未读");
        } catch (Exception e) {
            return Ret.fail("msg", "批量标记未读失败：" + e.getMessage());
        }
    }

    /**
     * 批量处理邮件
     *
     * @param ids 邮件ID列表
     * @return 操作结果
     */
    public Ret batchFollowUp(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Ret.fail("msg", "邮件ID列表不能为空");
        }

        try {
            Long currentUserId = JBoltUserKit.getUserId();
            Date currentTime = new Date();

            StringBuilder sql = new StringBuilder(
                    "UPDATE email_messages SET is_follow_up = 1, follow_up_id = ?, follow_up_time = ?, updated_at = ?, follow_type = 1 WHERE id IN (");
            List<Object> params = new ArrayList<>();
            params.add(currentUserId);
            params.add(currentTime);
            params.add(currentTime);

            for (int i = 0; i < ids.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
                params.add(Long.valueOf(ids.get(i)));
            }
            sql.append(")");

            int count = Db.update(sql.toString(), params.toArray());
            return Ret.ok("msg", "成功标记 " + count + " 封邮件为处理");
        } catch (Exception e) {
            return Ret.fail("msg", "批量处理失败：" + e.getMessage());
        }
    }

    /**
     * 批量标记邮件重要
     *
     * @param ids 邮件ID列表
     * @return 操作结果
     */
    public Ret batchMarkImportant(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Ret.fail("msg", "邮件ID列表不能为空");
        }

        try {
            StringBuilder sql = new StringBuilder(
                    "UPDATE email_messages SET is_important = 1, updated_at = ? WHERE id IN (");
            List<Object> params = new ArrayList<>();
            params.add(new Date());

            for (int i = 0; i < ids.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
                params.add(Long.valueOf(ids.get(i)));
            }
            sql.append(")");

            int count = Db.update(sql.toString(), params.toArray());
            return Ret.ok("msg", "成功标记 " + count + " 封邮件为重要");
        } catch (Exception e) {
            return Ret.fail("msg", "批量标记重要失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除邮件（软删除）
     *
     * @param ids 邮件ID列表
     * @return 操作结果
     */
    public Ret batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Ret.fail("msg", "邮件ID列表不能为空");
        }

        try {
            StringBuilder sql = new StringBuilder(
                    "UPDATE email_messages SET is_delete = 1, updated_at = ? WHERE id IN (");
            List<Object> params = new ArrayList<>();
            params.add(new Date());

            for (int i = 0; i < ids.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append("?");
                params.add(Long.valueOf(ids.get(i)));
            }
            sql.append(")");

            int count = Db.update(sql.toString(), params.toArray());
            return Ret.ok("msg", "成功删除 " + count + " 封邮件");
        } catch (Exception e) {
            return Ret.fail("msg", "批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取某个邮箱账户的所有邮件
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param params   查询参数
     * @return 查询结果
     */
    public Ret getEmailsByAccount(int page, int pageSize, Kv params) {
        try {
            String emailAccount = params.getStr("emailAccount");
            if (StrKit.isBlank(emailAccount)) {
                return Ret.fail("邮箱账户不能为空");
            }

            // 获取当前登录用户ID
            Long currentUserId = JBoltUserKit.getUserId();
            if (currentUserId == null) {
                return Ret.fail("用户未登录");
            }

            // 检查邮箱账户是否是当前用户管理的邮箱账号
            boolean isUserManagedEmail = Db.queryInt("SELECT COUNT(*) FROM user_email WHERE user_id = ? AND email = ?",
                currentUserId, emailAccount) > 0;

            if (!isUserManagedEmail) {
                LogKit.warn("[邮箱邮件] 用户 {} 尝试访问无权限的邮箱账户: {}", currentUserId, emailAccount);
                return Ret.fail("您没有权限查看该邮箱账户的邮件");
            }

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            LogKit.info("[邮箱邮件] 开始获取邮箱邮件, 邮箱: {}, 页码: {}, 每页: {}, 用户ID: {}", emailAccount, page, pageSize, currentUserId);

            // 构建 SQL 查询
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, ");
            sqlBuilder.append("s.from_address, s.to_address, s.cc_address, s.sent_date, s.has_attachments, ");
            sqlBuilder.append("s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, s.follow_type, ");
            sqlBuilder.append("u.name as follow_up_user_name, s.is_important, ");
            sqlBuilder.append("substr(s.content_html, 1, 500) as content_html, ");
            sqlBuilder.append("CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related ");
            sqlBuilder.append("FROM email_messages s ");
            sqlBuilder.append("LEFT JOIN jb_user u ON s.follow_up_id = u.id ");
            sqlBuilder.append("LEFT JOIN vw_user_email v ON s.id = v.emails_id ");
            sqlBuilder.append("WHERE s.email_account = ? ");
            sqlBuilder.append("AND ifnull(s.is_delete, 0) <> 1 ");

            // 添加用户权限控制：只能查看用户有权限的邮件
            // 参考getEmailHistory方法的权限控制逻辑
            sqlBuilder.append("AND v.user_id = ? ");

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(emailAccount);
            sqlParams.add(currentUserId);

            // 处理文件夹过滤
            if (StrKit.notBlank(params.getStr("folderName"))) {
                sqlBuilder.append("AND s.folder_name = ? ");
                sqlParams.add(params.getStr("folderName"));
            }

            // 处理其他过滤条件
            if (StrKit.notBlank(params.getStr("typeFilter"))) {
                String typeFilter = params.getStr("typeFilter");
                if ("received".equals(typeFilter)) {
                    sqlBuilder.append("AND s.from_address != ? ");
                    sqlParams.add(emailAccount);
                } else if ("sent".equals(typeFilter)) {
                    sqlBuilder.append("AND s.from_address = ? ");
                    sqlParams.add(emailAccount);
                }
            }

            if (StrKit.notBlank(params.getStr("readStatusFilter"))) {
                String readStatusFilter = params.getStr("readStatusFilter");
                if ("read".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 1 ");
                } else if ("unread".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 0 ");
                }
            }

            if (StrKit.notBlank(params.getStr("subjectSearch"))) {
                sqlBuilder.append("AND (s.subject LIKE ? OR s.content_html LIKE ?) ");
                String searchTerm = "%" + params.getStr("subjectSearch") + "%";
                sqlParams.add(searchTerm);
                sqlParams.add(searchTerm);
            }

            // 添加GROUP BY确保邮件ID唯一
            sqlBuilder.append("GROUP BY s.id ");

            // 构建排序
            String sortField = params.getStr("sortField", "sent_date");
            String sortDirection = params.getStr("sortDirection", "desc");
            if ("subject".equals(sortField)) {
                sqlBuilder.append("ORDER BY s.subject ").append(sortDirection);
            } else {
                sqlBuilder.append("ORDER BY s.sent_date ").append(sortDirection);
            }

            // 查询总数（用于分页）
            String countSql = "SELECT COUNT(*) FROM (SELECT s.id " +
                    sqlBuilder.substring(sqlBuilder.indexOf("FROM"), sqlBuilder.indexOf("ORDER BY")) + ") AS temp";

            long beforeCountQuery = System.currentTimeMillis();
            Integer totalCount = Db.queryInt(countSql, sqlParams.toArray());
            long countQueryTime = System.currentTimeMillis() - beforeCountQuery;
            LogKit.info("[邮箱邮件] 统计查询耗时: {}ms, 总数: {}", countQueryTime, totalCount);

            // 计算分页信息
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            int offset = (page - 1) * pageSize;

            // 添加分页限制
            sqlBuilder.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 执行主查询
            long beforeQuery = System.currentTimeMillis();
            List<Record> emails = Db.find(sqlBuilder.toString(), sqlParams.toArray());
            long queryTime = System.currentTimeMillis() - beforeQuery;
            LogKit.info("[邮箱邮件] 数据查询耗时: {}ms, 获取邮件数量: {}", queryTime, emails.size());

            // 处理邮件数据
            long beforeProcessing = System.currentTimeMillis();
            processEmailData(emails, emailAccount);
            long processingTime = System.currentTimeMillis() - beforeProcessing;
            LogKit.info("[邮箱邮件] 数据处理耗时: {}ms", processingTime);

            // 获取该邮箱的文件夹列表（只获取用户有权限的邮件的文件夹）
            List<Record> folders = Db.find("SELECT DISTINCT s.folder_name FROM email_messages s " +
                "LEFT JOIN vw_user_email v ON s.id = v.emails_id " +
                "WHERE s.email_account = ? AND v.user_id = ? ORDER BY s.folder_name",
                emailAccount, currentUserId);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", emails);
            result.put("total", totalCount);
            result.put("currentPage", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            result.put("folders", folders);

            long totalTime = System.currentTimeMillis() - startTime;
            LogKit.info("[邮箱邮件] 获取邮箱邮件完成，总耗时: {}ms", totalTime);

            return Ret.ok("data", result);
            
        } catch (Exception e) {
            LogKit.error("获取邮箱邮件失败", e);
            return Ret.fail("获取邮箱邮件失败：" + e.getMessage());
        }
    }

    /**
     * 获取某个公司的所有邮件
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param params   查询参数
     * @return 查询结果
     */
    public Ret getEmailsByCompany(int page, int pageSize, Kv params) {
        try {
            Integer companyId = params.getInt("companyId");
            if (companyId == null) {
                return Ret.fail("公司ID不能为空");
            }

            // 获取当前登录用户ID
            Long currentUserId = JBoltUserKit.getUserId();
            if (currentUserId == null) {
                return Ret.fail("用户未登录");
            }

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            LogKit.info("[公司邮件] 开始获取公司邮件, 公司ID: {}, 页码: {}, 每页: {}, 用户ID: {}", companyId, page, pageSize, currentUserId);

            // 获取该公司下当前用户有权限查看的客户邮箱
            // 结合user_email表来判断用户权限，只返回用户可以查看的邮箱
            List<String> clientEmails = Db.query("SELECT DISTINCT c.email FROM client c " +
                    "INNER JOIN company_client cc ON c.id = cc.client_id " +
                    "INNER JOIN user_email ue ON c.email = ue.email " +
                    "WHERE cc.company_id = ? AND ue.user_id = ? AND c.email IS NOT NULL AND c.email != ''",
                    companyId, currentUserId);

            if (clientEmails.isEmpty()) {
                return Ret.ok("data", Map.of(
                    "list", new ArrayList<>(),
                    "total", 0,
                    "currentPage", page,
                    "pageSize", pageSize,
                    "totalPages", 0,
                    "clients", new ArrayList<>()
                ));
            }

            // 构建 SQL 查询
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, ");
            sqlBuilder.append("s.from_address, s.to_address, s.cc_address, s.sent_date, s.has_attachments, ");
            sqlBuilder.append("s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, s.follow_type, ");
            sqlBuilder.append("u.name as follow_up_user_name, s.is_important, ");
            sqlBuilder.append("substr(s.content_html, 1, 500) as content_html, ");
            sqlBuilder.append("CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related ");
            sqlBuilder.append("FROM email_messages s ");
            sqlBuilder.append("LEFT JOIN jb_user u ON s.follow_up_id = u.id ");
            sqlBuilder.append("LEFT JOIN vw_user_email v ON s.id = v.emails_id ");
            sqlBuilder.append("WHERE (");

            List<Object> sqlParams = new ArrayList<>();

            // 为每个客户邮箱添加查询条件
            for (int i = 0; i < clientEmails.size(); i++) {
                if (i > 0) {
                    sqlBuilder.append(" OR ");
                }
                sqlBuilder.append("s.from_address = ? OR s.to_address LIKE ? OR s.cc_address LIKE ?");
                String email = clientEmails.get(i);
                sqlParams.add(email);
                sqlParams.add("%" + email + "%");
                sqlParams.add("%" + email + "%");
            }

            sqlBuilder.append(") AND ifnull(s.is_delete, 0) <> 1 ");

            // 添加用户权限控制：只能查看用户有权限的邮件
            // 参考getEmailHistory方法的权限控制逻辑
            sqlBuilder.append("AND v.user_id = ? ");
            sqlParams.add(currentUserId);

            // 处理客户邮箱过滤
            if (StrKit.notBlank(params.getStr("clientEmail"))) {
                String clientEmail = params.getStr("clientEmail");
                sqlBuilder.append("AND (s.from_address = ? OR s.to_address LIKE ? OR s.cc_address LIKE ?) ");
                sqlParams.add(clientEmail);
                sqlParams.add("%" + clientEmail + "%");
                sqlParams.add("%" + clientEmail + "%");
            }

            // 处理其他过滤条件
            if (StrKit.notBlank(params.getStr("typeFilter"))) {
                String typeFilter = params.getStr("typeFilter");
                if ("received".equals(typeFilter)) {
                    // 接收的邮件：发件人是客户邮箱之一
                    sqlBuilder.append("AND s.from_address IN (");
                    for (int i = 0; i < clientEmails.size(); i++) {
                        if (i > 0) sqlBuilder.append(",");
                        sqlBuilder.append("?");
                        sqlParams.add(clientEmails.get(i));
                    }
                    sqlBuilder.append(") ");
                } else if ("sent".equals(typeFilter)) {
                    // 发送的邮件：收件人包含客户邮箱
                    sqlBuilder.append("AND s.from_address NOT IN (");
                    for (int i = 0; i < clientEmails.size(); i++) {
                        if (i > 0) sqlBuilder.append(",");
                        sqlBuilder.append("?");
                        sqlParams.add(clientEmails.get(i));
                    }
                    sqlBuilder.append(") ");
                }
            }

            if (StrKit.notBlank(params.getStr("readStatusFilter"))) {
                String readStatusFilter = params.getStr("readStatusFilter");
                if ("read".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 1 ");
                } else if ("unread".equals(readStatusFilter)) {
                    sqlBuilder.append("AND s.is_read = 0 ");
                }
            }

            if (StrKit.notBlank(params.getStr("subjectSearch"))) {
                sqlBuilder.append("AND (s.subject LIKE ? OR s.content_html LIKE ?) ");
                String searchTerm = "%" + params.getStr("subjectSearch") + "%";
                sqlParams.add(searchTerm);
                sqlParams.add(searchTerm);
            }

            // 添加GROUP BY确保邮件ID唯一
            sqlBuilder.append("GROUP BY s.id ");

            // 构建排序
            String sortField = params.getStr("sortField", "sent_date");
            String sortDirection = params.getStr("sortDirection", "desc");
            if ("subject".equals(sortField)) {
                sqlBuilder.append("ORDER BY s.subject ").append(sortDirection);
            } else {
                sqlBuilder.append("ORDER BY s.sent_date ").append(sortDirection);
            }

            // 查询总数（用于分页）
            String countSql = "SELECT COUNT(*) FROM (SELECT s.id " +
                    sqlBuilder.substring(sqlBuilder.indexOf("FROM"), sqlBuilder.indexOf("ORDER BY")) + ") AS temp";

            long beforeCountQuery = System.currentTimeMillis();
            Integer totalCount = Db.queryInt(countSql, sqlParams.toArray());
            long countQueryTime = System.currentTimeMillis() - beforeCountQuery;
            LogKit.info("[公司邮件] 统计查询耗时: {}ms, 总数: {}", countQueryTime, totalCount);

            // 计算分页信息
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            int offset = (page - 1) * pageSize;

            // 添加分页限制
            sqlBuilder.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 执行主查询
            long beforeQuery = System.currentTimeMillis();
            List<Record> emails = Db.find(sqlBuilder.toString(), sqlParams.toArray());
            long queryTime = System.currentTimeMillis() - beforeQuery;
            LogKit.info("[公司邮件] 数据查询耗时: {}ms, 获取邮件数量: {}", queryTime, emails.size());

            // 处理邮件数据
            long beforeProcessing = System.currentTimeMillis();
            processEmailData(emails, null);
            long processingTime = System.currentTimeMillis() - beforeProcessing;
            LogKit.info("[公司邮件] 数据处理耗时: {}ms", processingTime);

            // 获取公司下的客户列表用于过滤
            List<Record> clients = Db.find("SELECT c.email, c.name FROM client c " +
                    "INNER JOIN company_client cc ON c.id = cc.client_id " +
                    "WHERE cc.company_id = ? AND c.email IS NOT NULL AND c.email != '' " +
                    "ORDER BY c.name", companyId);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", emails);
            result.put("total", totalCount);
            result.put("currentPage", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);
            result.put("clients", clients);

            long totalTime = System.currentTimeMillis() - startTime;
            LogKit.info("[公司邮件] 获取公司邮件完成，总耗时: {}ms", totalTime);

            return Ret.ok("data", result);
            
        } catch (Exception e) {
            LogKit.error("获取公司邮件失败", e);
            return Ret.fail("获取公司邮件失败：" + e.getMessage());
        }
    }

    /**
     * 处理邮件数据的通用方法
     */
    private void processEmailData(List<Record> emails, String targetEmail) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm");

        for (Record emailRecord : emails) {
            // 处理邮件账户显示名称
            String emailAccount = emailRecord.getStr("email_account");
            if (emailAccount != null) {
                String accountName = getDisplayName(emailAccount);
                emailRecord.set("account_name", accountName);
                emailRecord.set("accountName", accountName);
            } else {
                emailRecord.set("account_name", "");
                emailRecord.set("accountName", "");
            }

            // 处理发件人显示名称
            String fromAddress = emailRecord.getStr("from_address");
            if (fromAddress != null) {
                String fromName = getDisplayName(fromAddress);
                emailRecord.set("from_name", fromName);
                emailRecord.set("fromName", fromName);
            } else {
                emailRecord.set("from_name", "");
                emailRecord.set("fromName", "");
            }

            // 处理收件人显示名称
            String toAddress = emailRecord.getStr("to_address");
            if (toAddress != null) {
                String toName = getDisplayName(toAddress);
                emailRecord.set("to_name", toName);
                emailRecord.set("toName", toName);
            } else {
                emailRecord.set("to_name", "");
                emailRecord.set("toName", "");
            }

            // 格式化日期
            Date sentDate = emailRecord.getDate("sent_date");
            if (sentDate != null) {
                String formattedDate = dateFormat.format(sentDate);
                emailRecord.set("sentdate", formattedDate);
                emailRecord.set("sentDate", formattedDate);
            } else {
                emailRecord.set("sentdate", "");
                emailRecord.set("sentDate", "");
            }

            // 处理邮件内容
            String contentHtml = emailRecord.getStr("content_html");
            if (contentHtml != null) {
                // 使用正则表达式移除所有<p><br></p>
                contentHtml = contentHtml.replaceAll("<p><br></p>", "");
                emailRecord.set("content_html", contentHtml);
            }

            // 提取纯文本内容的前100个字符
            if (contentHtml != null && !contentHtml.trim().isEmpty()) {
                // 去除HTML标签，提取纯文本
                String plainText = extractPlainText(contentHtml);
                // 截取前100个字符
                if (plainText.length() > 100) {
                    plainText = plainText.substring(0, 100) + "...";
                }
                emailRecord.set("content_text", plainText);
                emailRecord.set("content_preview", plainText);
            } else {
                emailRecord.set("content_text", "");
                emailRecord.set("content_preview", "");
            }

            // 判断邮件方向：从公司邮箱账号（email_account）的角度
            String emailAccountField = emailRecord.getStr("email_account");
            Boolean isCustomerRelated = emailRecord.getBoolean("is_customer_related");

            boolean isSent = false;
            if (Boolean.TRUE.equals(isCustomerRelated)) {
                // 对于客户邮件：判断哪个地址是客户邮箱
                boolean isFromClient = isClientEmail(fromAddress);
                boolean isToClient = isClientEmail(toAddress);

                if (isFromClient && !isToClient) {
                    // from是客户，to不是客户 -> 收到客户邮件
                    isSent = false;
                } else if (!isFromClient && isToClient) {
                    // from不是客户，to是客户 -> 发给客户邮件
                    isSent = true;
                } else {
                    // 其他情况，使用默认逻辑
                    isSent = emailAccountField != null && emailAccountField.equals(fromAddress);
                }
            } else {
                // 对于非客户邮件：email_account与from_address相同是发出，其他情况是收到
                isSent = emailAccountField != null && emailAccountField.equals(fromAddress);
            }

            emailRecord.set("is_sent", isSent);
            emailRecord.set("is_client_email", isCustomerRelated);

            // 确保布尔值字段的一致性
            Boolean hasAttachments = emailRecord.getBoolean("has_attachments");
            emailRecord.set("has_attachments", hasAttachments != null && hasAttachments);

            Boolean isRead = emailRecord.getBoolean("is_read");
            emailRecord.set("is_read", isRead != null && isRead);

            Boolean isImportant = emailRecord.getBoolean("is_important");
            emailRecord.set("is_important", isImportant != null && isImportant);

            Boolean isFollowUp = emailRecord.getBoolean("is_follow_up");
            emailRecord.set("is_follow_up", isFollowUp != null && isFollowUp);
        }
    }

    /**
     * 判断邮箱地址是否为客户邮箱
     * @param emailAddresses 邮箱地址（可能包含多个地址，用逗号或分号分隔）
     * @return 是否包含客户邮箱
     */
    public boolean isClientEmail(String emailAddresses) {
        if (emailAddresses == null || emailAddresses.trim().isEmpty()) {
            return false;
        }

        try {
            // 处理多个邮箱地址的情况
            String[] emails = emailAddresses.split("[,;]");
            for (String email : emails) {
                String cleanEmail = email.trim();
                if (!cleanEmail.isEmpty()) {
                    // 提取纯邮箱地址（去除显示名称）
                    String pureEmail = extractEmailAddress(cleanEmail);
                    if (pureEmail != null) {
                        // 查询client表，检查邮箱是否存在
                        Integer count = Db.queryInt("SELECT COUNT(*) FROM client WHERE email = ?", pureEmail);
                        if (count != null && count > 0) {
                            return true; // 只要有一个是客户邮箱就返回true
                        }
                    }
                }
            }
            return false;
        } catch (Exception e) {
            LogKit.error("检查客户邮箱失败: " + emailAddresses, e);
            return false;
        }
    }

    /**
     * 从邮件地址字符串中提取纯邮箱地址
     * @param addressString 邮件地址字符串，可能包含显示名称，如 "张三 <<EMAIL>>"
     * @return 纯邮箱地址，如 "<EMAIL>"
     */
    private String extractEmailAddress(String addressString) {
        if (addressString == null || addressString.trim().isEmpty()) {
            return null;
        }

        String trimmed = addressString.trim();

        // 匹配 "显示名称 <<EMAIL>>" 格式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(".*<(.+?)>");
        java.util.regex.Matcher matcher = pattern.matcher(trimmed);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // 如果没有尖括号，直接返回（假设整个字符串就是邮箱地址）
        return trimmed;
    }

    /**
     * 获取Dashboard邮件列表（支持过滤和分页）
     *
     * @param page     页码
     * @param pageSize 每页大小  
     * @param params   查询参数
     * @return 查询结果
     */
    public Ret getDashboardEmails(int page, int pageSize, Kv params) {
        try {
            Long userId = params.getLong("userId");
            if (userId == null) {
                return Ret.fail("用户ID不能为空");
            }

            // 记录开始时间
            long startTime = System.currentTimeMillis();
            LogKit.info("[Dashboard邮件] 开始获取Dashboard邮件, 用户ID: {}, 页码: {}, 每页: {}", userId, page, pageSize);

            // 构建 SQL 查询
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT s.id, s.is_draft, s.subject, s.email_account, s.folder_name, ");
            sqlBuilder.append("s.from_address, s.to_address, s.cc_address, s.sent_date, s.has_attachments, ");
            sqlBuilder.append("s.is_read, s.is_follow_up, s.follow_up_id, s.follow_up_time, s.follow_type, ");
            sqlBuilder.append("u.name as follow_up_user_name, s.is_important, ");
            sqlBuilder.append("substr(s.content_html, 1, 500) as content_html, ");
            sqlBuilder.append("CASE WHEN v.client_id is not null and v.client_id <> '' THEN 1 ELSE 0 END as is_customer_related ");
            sqlBuilder.append("FROM email_messages s ");
            sqlBuilder.append("LEFT JOIN jb_user u ON s.follow_up_id = u.id ");
            sqlBuilder.append("LEFT JOIN vw_user_email v ON s.id = v.emails_id ");
            sqlBuilder.append("WHERE v.user_id = ? ");
            sqlBuilder.append("AND (v.email_account_id is null or s.account_id = v.email_account_id) ");
            sqlBuilder.append("AND ifnull(s.is_delete, 0) <> 1 ");

            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(userId);

            // 处理客户邮件过滤
            Boolean customerOnly = params.getBoolean("customerOnly");
            if (Boolean.TRUE.equals(customerOnly)) {
                sqlBuilder.append("AND v.client_id is not null and v.client_id <> '' ");
            }

            // 处理时间范围过滤
            String startDateStr = params.getStr("startDate");
            String endDateStr = params.getStr("endDate");
            LogKit.info("[Dashboard邮件] 时间范围参数 - startDate: {}, endDate: {}, days: {}",
                startDateStr, endDateStr, params.getStr("days"));

            if (StrKit.notBlank(startDateStr) && StrKit.notBlank(endDateStr)) {
                sqlBuilder.append("AND s.sent_date >= ? AND s.sent_date <= ? ");
                sqlParams.add(startDateStr);
                sqlParams.add(endDateStr);
                LogKit.info("[Dashboard邮件] 使用自定义时间范围: {} 至 {}", startDateStr, endDateStr);
            } else {
                // 默认查询最近几天
                Integer days = params.getInt("days", 1);
                if (days > 30) days = 30; // 限制最大30天
                sqlBuilder.append("AND s.sent_date >= DATE_SUB(NOW(), INTERVAL ? DAY) ");
                sqlParams.add(days);
                LogKit.info("[Dashboard邮件] 使用天数过滤: {} 天", days);
            }

            // 处理发件人过滤 - 支持邮箱地址和显示名称
            String senderFilter = params.getStr("senderFilter");
            if (StrKit.notBlank(senderFilter)) {
                sqlBuilder.append("AND (s.from_address LIKE ? OR s.from_display LIKE ?) ");
                sqlParams.add("%" + senderFilter + "%");
                sqlParams.add("%" + senderFilter + "%");
            }

            // 处理收件人过滤 - 支持邮箱地址和显示名称
            String recipientFilter = params.getStr("recipientFilter");
            if (StrKit.notBlank(recipientFilter)) {
                sqlBuilder.append("AND (s.to_address LIKE ? OR s.cc_address LIKE ? OR s.to_display LIKE ?) ");
                sqlParams.add("%" + recipientFilter + "%");
                sqlParams.add("%" + recipientFilter + "%");
                sqlParams.add("%" + recipientFilter + "%");
            }

            // 处理主题过滤
            String subjectFilter = params.getStr("subjectFilter");
            if (StrKit.notBlank(subjectFilter)) {
                sqlBuilder.append("AND s.subject LIKE ? ");
                sqlParams.add("%" + subjectFilter + "%");
            }

            // 处理内容过滤
            String contentFilter = params.getStr("contentFilter");
            if (StrKit.notBlank(contentFilter)) {
                sqlBuilder.append("AND (s.content_html LIKE ? OR s.content_text LIKE ?) ");
                sqlParams.add("%" + contentFilter + "%");
                sqlParams.add("%" + contentFilter + "%");
            }

            // 处理收件文件夹过滤
            String folderFilter = params.getStr("folderFilter");
            if (StrKit.notBlank(folderFilter)) {
                switch (folderFilter) {
                    case "inbox":
                        // 收件箱：folder_name为INBOX或其他非发送文件夹
                        sqlBuilder.append("AND (s.folder_name = 'INBOX' OR s.folder_name NOT IN ('Sent', 'Drafts', 'Draft', 'Outbox', 'Sent Items', '已发送', '草稿箱', '发件箱')) ");
                        break;
                    case "sent":
                        // 发件箱：folder_name为Sent或类似的已发送文件夹
                        sqlBuilder.append("AND (s.folder_name IN ('Sent', 'Outbox', 'Sent Items', '已发送', '发件箱') OR " +
                                         "(s.folder_name != 'Drafts' AND s.folder_name != 'Draft' AND s.folder_name != '草稿箱' AND " +
                                         "s.from_address IN (SELECT username FROM email_account WHERE id IN " +
                                         "(SELECT email_account_id FROM vw_user_email WHERE user_id = ?)))) ");
                        sqlParams.add(userId);
                        break;
                    case "draft":
                        // 草稿箱：folder_name为Drafts或is_draft=1
                        sqlBuilder.append("AND (s.folder_name IN ('Drafts', 'Draft', '草稿箱') OR s.is_draft = 1) ");
                        break;
                    case "unprocessed":
                        // 未处理邮件：is_follow_up = 0 或 is_follow_up IS NULL
                        sqlBuilder.append("AND (s.is_follow_up = 0 OR s.is_follow_up IS NULL) ");
                        break;
                    case "unread":
                        // 未读邮件：is_read = 0 或 is_read IS NULL
                        sqlBuilder.append("AND (s.is_read = 0 OR s.is_read IS NULL) ");
                        break;
                }
            }

            // 处理快速过滤条件
            String quickFilters = params.getStr("quickFilters");
            String filterLogic = params.getStr("filterLogic", "or");
            if (StrKit.notBlank(quickFilters)) {
                applyQuickFiltersToSQL(sqlBuilder, sqlParams, quickFilters, filterLogic, userId);
            }

            // 添加GROUP BY确保邮件ID唯一
            sqlBuilder.append("GROUP BY s.id ");

            // 构建排序
            String sortField = params.getStr("sortField", "sent_date");
            String sortDirection = params.getStr("sortDirection", "desc");
            if ("subject".equals(sortField)) {
                sqlBuilder.append("ORDER BY s.subject ").append(sortDirection);
            } else {
                sqlBuilder.append("ORDER BY s.sent_date ").append(sortDirection);
            }

            // 查询总数（用于分页）
            String countSql = "SELECT COUNT(*) FROM (SELECT s.id " +
                    sqlBuilder.substring(sqlBuilder.indexOf("FROM"), sqlBuilder.indexOf("ORDER BY")) + ") AS temp";

            long beforeCountQuery = System.currentTimeMillis();
            Integer totalCount = Db.queryInt(countSql, sqlParams.toArray());
            long countQueryTime = System.currentTimeMillis() - beforeCountQuery;
            LogKit.info("[Dashboard邮件] 统计查询耗时: {}ms, 总数: {}", countQueryTime, totalCount);

            // 计算分页信息
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            int offset = (page - 1) * pageSize;

            // 添加分页限制
            sqlBuilder.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 执行主查询
            long beforeQuery = System.currentTimeMillis();
            List<Record> emails = Db.find(sqlBuilder.toString(), sqlParams.toArray());
            long queryTime = System.currentTimeMillis() - beforeQuery;
            LogKit.info("[Dashboard邮件] 数据查询耗时: {}ms, 获取邮件数量: {}", queryTime, emails.size());

            // 处理邮件数据
            long beforeProcessing = System.currentTimeMillis();
            processEmailData(emails, null);
            long processingTime = System.currentTimeMillis() - beforeProcessing;
            LogKit.info("[Dashboard邮件] 数据处理耗时: {}ms", processingTime);

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", emails);
            result.put("total", totalCount);
            result.put("currentPage", page);
            result.put("pageSize", pageSize);
            result.put("totalPages", totalPages);

            long totalTime = System.currentTimeMillis() - startTime;
            LogKit.info("[Dashboard邮件] 获取Dashboard邮件完成，总耗时: {}ms", totalTime);

            return Ret.ok("data", result);

        } catch (Exception e) {
            LogKit.error("获取Dashboard邮件失败", e);
            return Ret.fail("获取Dashboard邮件失败：" + e.getMessage());
        }
    }

    /**
     * 将快速过滤条件应用到SQL查询中
     */
    private void applyQuickFiltersToSQL(StringBuilder sqlBuilder, List<Object> sqlParams, 
                                       String quickFilters, String filterLogic, Long userId) {
        try {
            // 解析JSON格式的快速过滤条件
            // quickFilters 格式类似: {"direction":"incoming","read_status":"unread"}
            if (StrKit.isBlank(quickFilters) || quickFilters.equals("{}")) {
                return;
            }

            // 简单的JSON解析（避免引入复杂的JSON库）
            quickFilters = quickFilters.trim();
            if (quickFilters.startsWith("{") && quickFilters.endsWith("}")) {
                quickFilters = quickFilters.substring(1, quickFilters.length() - 1);
                
                String[] filters = quickFilters.split(",");
                List<String> conditions = new ArrayList<>();
                
                for (String filter : filters) {
                    String[] parts = filter.split(":");
                    if (parts.length == 2) {
                        String key = parts[0].trim().replaceAll("\"", "");
                        String value = parts[1].trim().replaceAll("\"", "");
                        
                        String condition = buildFilterCondition(key, value, sqlParams, userId);
                        if (StrKit.notBlank(condition)) {
                            conditions.add(condition);
                        }
                    }
                }
                
                if (!conditions.isEmpty()) {
                    sqlBuilder.append("AND (");
                    String logic = "and".equals(filterLogic) ? " AND " : " OR ";
                    sqlBuilder.append(String.join(logic, conditions));
                    sqlBuilder.append(") ");
                }
            }
        } catch (Exception e) {
            LogKit.warn("应用快速过滤条件失败: {}", e.getMessage());
        }
    }

    /**
     * 构建单个过滤条件的SQL
     */
    private String buildFilterCondition(String filter, String value, List<Object> sqlParams, Long userId) {
        switch (filter) {
            case "folder_type":
                if ("inbox".equals(value)) {
                    // 收件箱：folder_name为INBOX或其他非发送文件夹
                    return "(s.folder_name = 'INBOX' OR s.folder_name NOT IN ('Sent', 'Drafts', 'Draft', 'Outbox', 'Sent Items', '已发送', '草稿箱', '发件箱'))";
                } else if ("sent".equals(value)) {
                    // 发件箱：folder_name为Sent或类似的已发送文件夹
                    return "(s.folder_name IN ('Sent', 'Outbox', 'Sent Items', '已发送', '发件箱') OR " +
                           "(s.folder_name != 'Drafts' AND s.folder_name != 'Draft' AND s.folder_name != '草稿箱' AND " +
                           "s.from_address IN (SELECT username FROM email_account WHERE id IN " +
                           "(SELECT email_account_id FROM vw_user_email WHERE user_id = " + userId + "))))";
                } else if ("draft".equals(value)) {
                    // 草稿箱：folder_name为Drafts或is_draft=1
                    return "(s.folder_name IN ('Drafts', 'Draft', '草稿箱') OR s.is_draft = 1)";
                } else if ("unprocessed".equals(value)) {
                    // 未处理邮件：is_follow_up = 0 或 is_follow_up IS NULL
                    return "(s.is_follow_up = 0 OR s.is_follow_up IS NULL)";
                } else if ("unread".equals(value)) {
                    // 未读邮件：is_read = 0 或 is_read IS NULL
                    return "(s.is_read = 0 OR s.is_read IS NULL)";
                }
                break;

            case "direction":
                if ("incoming".equals(value)) {
                    // 收到的邮件：from_address不等于当前用户的邮箱账户
                    return "s.from_address NOT IN (SELECT username FROM email_account WHERE id IN " +
                           "(SELECT email_account_id FROM vw_user_email WHERE user_id = " + userId + "))";
                } else if ("outgoing".equals(value)) {
                    // 发出的邮件：from_address等于当前用户的邮箱账户
                    return "s.from_address IN (SELECT username FROM email_account WHERE id IN " +
                           "(SELECT email_account_id FROM vw_user_email WHERE user_id = " + userId + "))";
                }
                break;
                
            case "read_status":
                if ("read".equals(value)) {
                    return "s.is_read = 1";
                } else if ("unread".equals(value)) {
                    return "(s.is_read = 0 OR s.is_read IS NULL)";
                }
                break;
                
            case "follow_status":
                if ("followed".equals(value)) {
                    return "s.is_follow_up = 1";
                } else if ("not_followed".equals(value)) {
                    return "(s.is_follow_up = 0 OR s.is_follow_up IS NULL)";
                }
                break;
                
            case "has_attachments":
                if ("true".equals(value)) {
                    return "s.has_attachments = 1";
                }
                break;
                
            case "has_translation":
                if ("true".equals(value)) {
                    return "s.has_translation = 1";
                }
                break;
                
            case "is_important":
                if ("true".equals(value)) {
                    return "s.is_important = 1";
                }
                break;
                
            case "is_customer_email":
                if ("true".equals(value)) {
                    return "v.client_id is not null and v.client_id <> ''";
                }
                break;
        }
        return "";
    }

    /**
     * AI排版邮件内容
     * 
     * @param originalContent 原始邮件内容
     * @param subject 邮件主题
     * @return 排版后的内容
     */
    public String aiTypesetting(String originalContent, String subject) {
        try {
            if (StrKit.isBlank(originalContent)) {
                return null;
            }

            // 构建AI提示词
            String prompt = buildTypesettingPrompt(originalContent, subject);
            String result = LlmService.me().callLlm("硅基流动", "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B", prompt);
            
            if (StrKit.notBlank(result)) {
                LogKit.info("AI排版成功，原始长度: {}, 处理后长度: {}", originalContent.length(), result.length());
                return result;
            } else {
                LogKit.warn("AI排版返回空结果");
                return null;
            }
            
        } catch (Exception e) {
            LogKit.error("AI排版失败", e);
            return null;
        }
    }

    /**
     * 构建AI排版提示词
     */
    private String buildTypesettingPrompt(String originalContent, String subject) {

        String promptBuilder = "请对以下邮件内容进行重新排版和整理，使其更加清晰易读：\n\n" +
                "邮件主题：" + (subject != null ? subject : "无主题") + "\n\n" +
                "要求：\n" +
                "1. 保持所有原始信息和内容完整性，不要删除任何重要信息\n" +
                "2. 重新组织段落结构，使逻辑更清晰\n" +
                "3. 清理多余的空行和格式化字符\n" +
                "4. 区分不同的邮件部分（如原始邮件、转发邮件、回复等）\n" +
                "5. 保持HTML格式，但优化结构\n" +
                "6. 对于多次转发的邮件，按时间顺序清晰地分隔各个部分\n" +
                "7. 保留重要的元数据信息（发件人、收件人、时间等）\n" +
                "8. 如果有签名信息，将其单独分段显示\n\n" +
                "请直接返回整理后的HTML内容，无需额外说明：\n\n" +
                originalContent;
        
        return promptBuilder;
    }

    /**
     * 根据邮箱地址查找相关的公司信息
     *
     * @param fromAddress 发件人邮箱
     * @param toAddress 收件人邮箱
     * @param ccAddress 抄送人邮箱
     * @return 相关公司信息列表
     */
    public List<Record> getCompaniesFromEmails(String fromAddress, String toAddress, String ccAddress) {
        List<String> emails = new ArrayList<>();

        // 收集所有邮箱地址
        if (StrKit.notBlank(fromAddress)) {
            emails.addAll(extractEmailAddresses(fromAddress));
        }
        if (StrKit.notBlank(toAddress)) {
            emails.addAll(extractEmailAddresses(toAddress));
        }
        if (StrKit.notBlank(ccAddress)) {
            emails.addAll(extractEmailAddresses(ccAddress));
        }

        if (emails.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建SQL查询，查找邮箱对应的公司信息
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT c.id, c.name, c.nick_name, c.osclub_id ");
        sql.append("FROM company c ");
        sql.append("INNER JOIN company_client cc ON c.id = cc.company_id ");
        sql.append("INNER JOIN client cl ON cc.client_id = cl.id ");
        sql.append("WHERE cl.email IN (");

        // 添加占位符
        for (int i = 0; i < emails.size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append("?");
        }
        sql.append(") ORDER BY c.name");

        return Db.find(sql.toString(), emails.toArray());
    }

    /**
     * 从邮箱地址字符串中提取纯邮箱地址
     *
     * @param emailString 邮箱地址字符串，可能包含显示名称
     * @return 邮箱地址列表
     */
    private List<String> extractEmailAddresses(String emailString) {
        List<String> emails = new ArrayList<>();
        if (StrKit.isBlank(emailString)) {
            return emails;
        }

        // 处理多个邮箱地址的情况（用逗号或分号分隔）
        String[] emailParts = emailString.split("[,;]");
        for (String emailPart : emailParts) {
            String cleanEmail = extractSingleEmailAddress(emailPart.trim());
            if (StrKit.notBlank(cleanEmail)) {
                emails.add(cleanEmail);
            }
        }

        return emails;
    }

    /**
     * 从单个邮箱地址字符串中提取纯邮箱地址
     *
     * @param emailString 单个邮箱地址字符串
     * @return 纯邮箱地址
     */
    private String extractSingleEmailAddress(String emailString) {
        if (StrKit.isBlank(emailString)) {
            return null;
        }

        // 如果包含 < 和 >，提取其中的邮箱地址
        if (emailString.contains("<") && emailString.contains(">")) {
            int start = emailString.indexOf("<");
            int end = emailString.indexOf(">");
            if (start < end) {
                return emailString.substring(start + 1, end).trim();
            }
        }

        // 如果没有 < >，直接返回去除空格的字符串
        return emailString.trim();
    }

}